import { storage } from './config';
import { ref, uploadBytes, getDownloadURL, deleteObject } from 'firebase/storage';

export class StorageService {
  // Upload a file to Firebase Storage
  static async uploadFile(
    file: File, 
    path: string, 
    onProgress?: (progress: number) => void
  ): Promise<string> {
    try {
      // Create a reference to the file location
      const storageRef = ref(storage, path);
      
      // Upload the file
      const snapshot = await uploadBytes(storageRef, file);
      
      // Get the download URL
      const downloadURL = await getDownloadURL(snapshot.ref);
      
      return downloadURL;
    } catch (error) {
      console.error('Error uploading file:', error);
      throw new Error('Failed to upload file');
    }
  }

  // Upload template document
  static async uploadTemplateDocument(
    file: File,
    templateId: string,
    documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment'
  ): Promise<string> {
    const timestamp = Date.now();
    const fileExtension = file.name.split('.').pop();
    const fileName = `${documentType}_${timestamp}.${fileExtension}`;
    const path = `templates/${templateId}/documents/${fileName}`;
    
    return this.uploadFile(file, path);
  }

  // Delete a file from Firebase Storage
  static async deleteFile(url: string): Promise<void> {
    try {
      // Extract the path from the URL
      const urlParts = url.split('/');
      const pathIndex = urlParts.findIndex(part => part === 'o') + 1;
      if (pathIndex === 0) {
        throw new Error('Invalid Firebase Storage URL');
      }
      
      const encodedPath = urlParts[pathIndex].split('?')[0];
      const path = decodeURIComponent(encodedPath);
      
      const storageRef = ref(storage, path);
      await deleteObject(storageRef);
    } catch (error) {
      console.error('Error deleting file:', error);
      throw new Error('Failed to delete file');
    }
  }

  // Get file name from URL
  static getFileNameFromUrl(url: string): string {
    try {
      const urlParts = url.split('/');
      const pathIndex = urlParts.findIndex(part => part === 'o') + 1;
      if (pathIndex === 0) {
        return 'Unknown file';
      }
      
      const encodedPath = urlParts[pathIndex].split('?')[0];
      const path = decodeURIComponent(encodedPath);
      const fileName = path.split('/').pop() || 'Unknown file';
      
      // Remove timestamp prefix if present
      const parts = fileName.split('_');
      if (parts.length > 1 && !isNaN(Number(parts[parts.length - 1].split('.')[0]))) {
        return parts.slice(0, -1).join('_') + '.' + fileName.split('.').pop();
      }
      
      return fileName;
    } catch (error) {
      console.error('Error parsing file name from URL:', error);
      return 'Unknown file';
    }
  }

  // Validate file type and size
  static validateFile(
    file: File, 
    allowedTypes: string[] = ['.pdf', '.doc', '.docx', '.txt'],
    maxSizeMB: number = 10
  ): { isValid: boolean; error?: string } {
    // Check file size
    if (file.size > maxSizeMB * 1024 * 1024) {
      return {
        isValid: false,
        error: `File size must be less than ${maxSizeMB}MB`
      };
    }

    // Check file type
    const fileExtension = '.' + file.name.split('.').pop()?.toLowerCase();
    const isValidType = allowedTypes.includes(fileExtension);

    if (!isValidType) {
      return {
        isValid: false,
        error: `Please select a valid file type: ${allowedTypes.join(', ')}`
      };
    }

    return { isValid: true };
  }
}
