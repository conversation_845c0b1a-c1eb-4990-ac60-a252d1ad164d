{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/ThemeToggle.tsx"], "sourcesContent": ["'use client';\n\nimport { useThemeSafe } from '@/contexts/ThemeContext';\nimport { useState, useEffect } from 'react';\n\nexport default function ThemeToggle() {\n  const [mounted, setMounted] = useState(false);\n  const [theme, setTheme] = useState<'light' | 'dark'>('light');\n\n  // Use safe version that returns null instead of throwing\n  const themeContext = useThemeSafe();\n\n  useEffect(() => {\n    setMounted(true);\n\n    // If no context, manage theme locally\n    if (!themeContext) {\n      const savedTheme = localStorage.getItem('theme') as 'light' | 'dark';\n      if (savedTheme) {\n        setTheme(savedTheme);\n        applyTheme(savedTheme);\n      } else {\n        const systemPrefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;\n        const initialTheme = systemPrefersDark ? 'dark' : 'light';\n        setTheme(initialTheme);\n        applyTheme(initialTheme);\n      }\n    }\n  }, [themeContext]);\n\n  const applyTheme = (newTheme: 'light' | 'dark') => {\n    const root = document.documentElement;\n    if (newTheme === 'dark') {\n      root.classList.add('dark');\n    } else {\n      root.classList.remove('dark');\n    }\n  };\n\n  const handleToggle = () => {\n    if (themeContext) {\n      // Use context if available\n      themeContext.toggleTheme();\n    } else {\n      // Handle locally if no context\n      const newTheme = theme === 'light' ? 'dark' : 'light';\n      setTheme(newTheme);\n      applyTheme(newTheme);\n      localStorage.setItem('theme', newTheme);\n    }\n  };\n\n  // Don't render until mounted to prevent hydration mismatch\n  if (!mounted) {\n    return (\n      <div className=\"p-2 w-9 h-9\">\n        {/* Placeholder to maintain layout */}\n      </div>\n    );\n  }\n\n  const currentTheme = themeContext ? themeContext.theme : theme;\n\n  return (\n    <button\n      onClick={handleToggle}\n      className=\"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors\"\n      aria-label={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n      title={`Switch to ${currentTheme === 'light' ? 'dark' : 'light'} mode`}\n    >\n      {currentTheme === 'light' ? (\n        // Moon icon for dark mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z\"\n          />\n        </svg>\n      ) : (\n        // Sun icon for light mode\n        <svg\n          className=\"w-5 h-5\"\n          fill=\"none\"\n          stroke=\"currentColor\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <path\n            strokeLinecap=\"round\"\n            strokeLinejoin=\"round\"\n            strokeWidth={2}\n            d=\"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z\"\n          />\n        </svg>\n      )}\n    </button>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAHA;;;;AAKe,SAAS;IACtB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;IAErD,yDAAyD;IACzD,MAAM,eAAe,CAAA,GAAA,yHAAA,CAAA,eAAY,AAAD;IAEhC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,WAAW;QAEX,sCAAsC;QACtC,IAAI,CAAC,cAAc;YACjB,MAAM,aAAa,aAAa,OAAO,CAAC;YACxC,IAAI,YAAY;gBACd,SAAS;gBACT,WAAW;YACb,OAAO;gBACL,MAAM,oBAAoB,OAAO,UAAU,CAAC,gCAAgC,OAAO;gBACnF,MAAM,eAAe,oBAAoB,SAAS;gBAClD,SAAS;gBACT,WAAW;YACb;QACF;IACF,GAAG;QAAC;KAAa;IAEjB,MAAM,aAAa,CAAC;QAClB,MAAM,OAAO,SAAS,eAAe;QACrC,IAAI,aAAa,QAAQ;YACvB,KAAK,SAAS,CAAC,GAAG,CAAC;QACrB,OAAO;YACL,KAAK,SAAS,CAAC,MAAM,CAAC;QACxB;IACF;IAEA,MAAM,eAAe;QACnB,IAAI,cAAc;YAChB,2BAA2B;YAC3B,aAAa,WAAW;QAC1B,OAAO;YACL,+BAA+B;YAC/B,MAAM,WAAW,UAAU,UAAU,SAAS;YAC9C,SAAS;YACT,WAAW;YACX,aAAa,OAAO,CAAC,SAAS;QAChC;IACF;IAEA,2DAA2D;IAC3D,IAAI,CAAC,SAAS;QACZ,qBACE,8OAAC;YAAI,WAAU;;;;;;IAInB;IAEA,MAAM,eAAe,eAAe,aAAa,KAAK,GAAG;IAEzD,qBACE,8OAAC;QACC,SAAS;QACT,WAAU;QACV,cAAY,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;QAC3E,OAAO,CAAC,UAAU,EAAE,iBAAiB,UAAU,SAAS,QAAQ,KAAK,CAAC;kBAErE,iBAAiB,UAChB,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;mBAIN,0BAA0B;sBAC1B,8OAAC;YACC,WAAU;YACV,MAAK;YACL,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,8OAAC;gBACC,eAAc;gBACd,gBAAe;gBACf,aAAa;gBACb,GAAE;;;;;;;;;;;;;;;;AAMd", "debugId": null}}, {"offset": {"line": 155, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/TrialBanner.tsx"], "sourcesContent": ["'use client';\n\nimport { useState } from 'react';\nimport Link from 'next/link';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { getTrialStatusMessage } from '@/lib/trial';\n\nexport default function TrialBanner() {\n  const { user } = useAuth();\n  const [isDismissed, setIsDismissed] = useState(false);\n\n  // Don't show banner if user is not a trial user, is admin, or banner is dismissed\n  if (!user || !user.isTrialUser || user.role === 'admin' || isDismissed) {\n    return null;\n  }\n\n  const { message, type, daysRemaining } = getTrialStatusMessage(user);\n\n  // Don't show banner if no message\n  if (!message) {\n    return null;\n  }\n\n  const getBannerStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200';\n      case 'warning':\n        return 'bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200';\n      default:\n        return 'bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200';\n    }\n  };\n\n  const getButtonStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white';\n      case 'warning':\n        return 'bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white';\n      default:\n        return 'bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white';\n    }\n  };\n\n  const getIconStyles = () => {\n    switch (type) {\n      case 'error':\n        return 'text-red-400 dark:text-red-300';\n      case 'warning':\n        return 'text-yellow-400 dark:text-yellow-300';\n      default:\n        return 'text-blue-400 dark:text-blue-300';\n    }\n  };\n\n  return (\n    <div className={`border-l-4 p-4 ${getBannerStyles()}`}>\n      <div className=\"flex items-center justify-between\">\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {type === 'error' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : type === 'warning' ? (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n              </svg>\n            ) : (\n              <svg className={`h-5 w-5 ${getIconStyles()}`} viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n              </svg>\n            )}\n          </div>\n          <div className=\"ml-3\">\n            <p className=\"text-sm font-medium\">\n              {message}\n            </p>\n          </div>\n        </div>\n        \n        <div className=\"flex items-center space-x-3\">\n          <Link\n            href=\"/pricing\"\n            className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${getButtonStyles()}`}\n          >\n            Upgrade Now\n          </Link>\n          \n          <button\n            onClick={() => setIsDismissed(true)}\n            className=\"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300\"\n            aria-label=\"Dismiss banner\"\n          >\n            <svg className=\"h-5 w-5\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n              <path fillRule=\"evenodd\" d=\"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z\" clipRule=\"evenodd\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n    </div>\n  );\n}\n\n// Compact version for navigation bar\nexport function TrialIndicator() {\n  const { user } = useAuth();\n\n  if (!user || !user.isTrialUser || user.role === 'admin') {\n    return null;\n  }\n\n  const { daysRemaining } = getTrialStatusMessage(user);\n\n  if (daysRemaining <= 0) {\n    return (\n      <Link\n        href=\"/pricing\"\n        className=\"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors\"\n      >\n        Trial Expired\n      </Link>\n    );\n  }\n\n  const getIndicatorStyles = () => {\n    if (daysRemaining <= 3) {\n      return 'bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800';\n    }\n    return 'bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800';\n  };\n\n  return (\n    <Link\n      href=\"/pricing\"\n      className={`px-3 py-1 text-xs font-medium rounded-full transition-colors ${getIndicatorStyles()}`}\n    >\n      {daysRemaining} day{daysRemaining === 1 ? '' : 's'} left\n    </Link>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAOe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACvB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE/C,kFAAkF;IAClF,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,WAAW,aAAa;QACtE,OAAO;IACT;IAEA,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAE/D,kCAAkC;IAClC,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBA<PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,kBAAkB;QACtB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,MAAM,gBAAgB;QACpB,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAW,CAAC,eAAe,EAAE,mBAAmB;kBACnD,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACZ,SAAS,wBACR,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAA0N,UAAS;;;;;;;;;;uCAE9P,SAAS,0BACX,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAoN,UAAS;;;;;;;;;;qDAG1P,8OAAC;gCAAI,WAAW,CAAC,QAAQ,EAAE,iBAAiB;gCAAE,SAAQ;gCAAY,MAAK;0CACrE,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAmI,UAAS;;;;;;;;;;;;;;;;sCAI7K,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAE,WAAU;0CACV;;;;;;;;;;;;;;;;;8BAKP,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BACH,MAAK;4BACL,WAAW,CAAC,2DAA2D,EAAE,mBAAmB;sCAC7F;;;;;;sCAID,8OAAC;4BACC,SAAS,IAAM,eAAe;4BAC9B,WAAU;4BACV,cAAW;sCAEX,cAAA,8OAAC;gCAAI,WAAU;gCAAU,SAAQ;gCAAY,MAAK;0CAChD,cAAA,8OAAC;oCAAK,UAAS;oCAAU,GAAE;oCAAqM,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOvP;AAGO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IAEvB,IAAI,CAAC,QAAQ,CAAC,KAAK,WAAW,IAAI,KAAK,IAAI,KAAK,SAAS;QACvD,OAAO;IACT;IAEA,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,4GAAA,CAAA,wBAAqB,AAAD,EAAE;IAEhD,IAAI,iBAAiB,GAAG;QACtB,qBACE,8OAAC,4JAAA,CAAA,UAAI;YACH,MAAK;YACL,WAAU;sBACX;;;;;;IAIL;IAEA,MAAM,qBAAqB;QACzB,IAAI,iBAAiB,GAAG;YACtB,OAAO;QACT;QACA,OAAO;IACT;IAEA,qBACE,8OAAC,4JAAA,CAAA,UAAI;QACH,MAAK;QACL,WAAW,CAAC,6DAA6D,EAAE,sBAAsB;;YAEhG;YAAc;YAAK,kBAAkB,IAAI,KAAK;YAAI;;;;;;;AAGzD", "debugId": null}}, {"offset": {"line": 401, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/Navigation.tsx"], "sourcesContent": ["'use client';\n\nimport Link from 'next/link';\nimport Image from 'next/image';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport ThemeToggle from './ThemeToggle';\nimport { TrialIndicator } from './TrialBanner';\n\ninterface NavigationProps {\n  title?: string;\n  showBackButton?: boolean;\n  backUrl?: string;\n  backText?: string;\n}\n\nexport default function Navigation({\n  title = 'VALTICS AI',\n  showBackButton = false,\n  backUrl = '/dashboard',\n  backText = '← Back to Dashboard'\n}: NavigationProps) {\n  const { user, logOut, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const handleLogout = async () => {\n    try {\n      await logOut();\n      router.push('/');\n    } catch (error) {\n      console.error('Error logging out:', error);\n    }\n  };\n\n  if (!user) {\n    return null;\n  }\n\n  return (\n    <nav className=\"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700\">\n      <div className=\"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\">\n        <div className=\"flex justify-between h-16\">\n          <div className=\"flex items-center\">\n            <Link href=\"/dashboard\" className=\"flex items-center space-x-3\">\n              <Image\n                src=\"/logo.png\"\n                alt=\"VALTICS AI Logo\"\n                width={32}\n                height={32}\n                className=\"w-8 h-8\"\n              />\n              <span className=\"text-xl font-semibold text-gray-900 dark:text-white\">\n                {title}\n              </span>\n            </Link>\n          </div>\n\n          <div className=\"flex items-center space-x-4\">\n            {showBackButton && (\n              <Link\n                href={backUrl}\n                className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n              >\n                {backText}\n              </Link>\n            )}\n\n            {!showBackButton && (\n              <>\n                <Link\n                  href=\"/brands\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Brands\n                </Link>\n                <Link\n                  href=\"/templates\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Templates\n                </Link>\n                {isAdmin && (\n                  <Link\n                    href=\"/admin\"\n                    className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                  >\n                    Admin\n                  </Link>\n                )}\n                <Link\n                  href=\"/profile\"\n                  className=\"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium\"\n                >\n                  Profile\n                </Link>\n              </>\n            )}\n\n            {/* Trial Indicator */}\n            <TrialIndicator />\n\n            {/* Theme Toggle */}\n            <ThemeToggle />\n\n            <button\n              onClick={handleLogout}\n              className=\"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800\"\n            >\n              Logout\n            </button>\n          </div>\n        </div>\n      </div>\n    </nav>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;AAgBe,SAAS,WAAW,EACjC,QAAQ,YAAY,EACpB,iBAAiB,KAAK,EACtB,UAAU,YAAY,EACtB,WAAW,qBAAqB,EAChB;IAChB,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACxC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,eAAe;QACnB,IAAI;YACF,MAAM;YACN,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;QACtC;IACF;IAEA,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAa,WAAU;;8CAChC,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAI;oCACJ,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;8CAEZ,8OAAC;oCAAK,WAAU;8CACb;;;;;;;;;;;;;;;;;kCAKP,8OAAC;wBAAI,WAAU;;4BACZ,gCACC,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM;gCACN,WAAU;0CAET;;;;;;4BAIJ,CAAC,gCACA;;kDACE,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAGD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;oCAGA,yBACC,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;kDAIH,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;0CAOL,8OAAC,0HAAA,CAAA,iBAAc;;;;;0CAGf,8OAAC,0HAAA,CAAA,UAAW;;;;;0CAEZ,8OAAC;gCACC,SAAS;gCACT,WAAU;0CACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQb", "debugId": null}}, {"offset": {"line": 576, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/SimpleRichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useRef } from 'react';\n\ninterface SimpleRichTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n}\n\nconst SimpleRichTextEditor: React.FC<SimpleRichTextEditorProps> = ({\n  value,\n  onChange,\n  placeholder = 'Enter text...',\n  className = '',\n  disabled = false\n}) => {\n  const editorRef = useRef<HTMLDivElement>(null);\n\n  const handleInput = () => {\n    if (editorRef.current) {\n      onChange(editorRef.current.innerHTML);\n    }\n  };\n\n  const formatText = (command: string, value?: string) => {\n    document.execCommand(command, false, value);\n    if (editorRef.current) {\n      editorRef.current.focus();\n      onChange(editorRef.current.innerHTML);\n    }\n  };\n\n  const insertList = (ordered: boolean) => {\n    const command = ordered ? 'insertOrderedList' : 'insertUnorderedList';\n    formatText(command);\n  };\n\n  return (\n    <div className={`simple-rich-text-editor ${className}`}>\n      {/* Toolbar */}\n      <div className=\"border border-gray-300 dark:border-gray-600 border-b-0 bg-gray-50 dark:bg-gray-700 p-2 flex flex-wrap gap-1 rounded-t-md\">\n        <button\n          type=\"button\"\n          onClick={() => formatText('bold')}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          <strong>B</strong>\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => formatText('italic')}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          <em>I</em>\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => formatText('underline')}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          <u>U</u>\n        </button>\n        <div className=\"w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n        <button\n          type=\"button\"\n          onClick={() => insertList(false)}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          • List\n        </button>\n        <button\n          type=\"button\"\n          onClick={() => insertList(true)}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-200 dark:hover:bg-gray-600 focus:outline-none\"\n          disabled={disabled}\n        >\n          1. List\n        </button>\n        <div className=\"w-px bg-gray-300 dark:bg-gray-600 mx-1\"></div>\n        <select\n          onChange={(e) => formatText('formatBlock', e.target.value)}\n          className=\"px-2 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none\"\n          disabled={disabled}\n          defaultValue=\"\"\n        >\n          <option value=\"\">Normal</option>\n          <option value=\"h1\">Heading 1</option>\n          <option value=\"h2\">Heading 2</option>\n          <option value=\"h3\">Heading 3</option>\n          <option value=\"h4\">Heading 4</option>\n          <option value=\"h5\">Heading 5</option>\n          <option value=\"h6\">Heading 6</option>\n        </select>\n      </div>\n\n      {/* Editor */}\n      <div\n        ref={editorRef}\n        contentEditable={!disabled}\n        onInput={handleInput}\n        dangerouslySetInnerHTML={{ __html: value }}\n        className={`\n          min-h-[150px] p-3 border border-gray-300 dark:border-gray-600 rounded-b-md\n          bg-white dark:bg-gray-700 text-gray-900 dark:text-white\n          focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500\n          ${disabled ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed' : ''}\n        `}\n        style={{\n          backgroundColor: disabled ? '#f9fafb' : undefined,\n        }}\n        data-placeholder={placeholder}\n      />\n\n      <style jsx>{`\n        .simple-rich-text-editor [contenteditable]:empty:before {\n          content: attr(data-placeholder);\n          color: #9ca3af;\n          pointer-events: none;\n        }\n        .simple-rich-text-editor [contenteditable] {\n          outline: none;\n        }\n        .simple-rich-text-editor [contenteditable] h1 {\n          font-size: 2em;\n          font-weight: bold;\n          margin: 0.67em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h2 {\n          font-size: 1.5em;\n          font-weight: bold;\n          margin: 0.75em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h3 {\n          font-size: 1.17em;\n          font-weight: bold;\n          margin: 0.83em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h4 {\n          font-size: 1em;\n          font-weight: bold;\n          margin: 1.12em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h5 {\n          font-size: 0.83em;\n          font-weight: bold;\n          margin: 1.5em 0;\n        }\n        .simple-rich-text-editor [contenteditable] h6 {\n          font-size: 0.75em;\n          font-weight: bold;\n          margin: 1.67em 0;\n        }\n        .simple-rich-text-editor [contenteditable] ul {\n          list-style-type: disc;\n          margin: 1em 0;\n          padding-left: 2em;\n        }\n        .simple-rich-text-editor [contenteditable] ol {\n          list-style-type: decimal;\n          margin: 1em 0;\n          padding-left: 2em;\n        }\n        .simple-rich-text-editor [contenteditable] li {\n          margin: 0.5em 0;\n        }\n        .simple-rich-text-editor [contenteditable] p {\n          margin: 1em 0;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default SimpleRichTextEditor;\n"], "names": [], "mappings": ";;;;;AAEA;AAFA;;;;AAYA,MAAM,uBAA4D,CAAC,EACjE,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EAC7B,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;IACC,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAkB;IAEzC,MAAM,cAAc;QAClB,IAAI,UAAU,OAAO,EAAE;YACrB,SAAS,UAAU,OAAO,CAAC,SAAS;QACtC;IACF;IAEA,MAAM,aAAa,CAAC,SAAiB;QACnC,SAAS,WAAW,CAAC,SAAS,OAAO;QACrC,IAAI,UAAU,OAAO,EAAE;YACrB,UAAU,OAAO,CAAC,KAAK;YACvB,SAAS,UAAU,OAAO,CAAC,SAAS;QACtC;IACF;IAEA,MAAM,aAAa,CAAC;QAClB,MAAM,UAAU,UAAU,sBAAsB;QAChD,WAAW;IACb;IAEA,qBACE,8OAAC;kDAAe,CAAC,wBAAwB,EAAE,WAAW;;0BAEpD,8OAAC;0DAAc;;kCACb,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAGV,cAAA,8OAAC;;sCAAO;;;;;;;;;;;kCAEV,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAGV,cAAA,8OAAC;;sCAAG;;;;;;;;;;;kCAEN,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAGV,cAAA,8OAAC;;sCAAE;;;;;;;;;;;kCAEL,8OAAC;kEAAc;;;;;;kCACf,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAEX;;;;;;kCAGD,8OAAC;wBACC,MAAK;wBACL,SAAS,IAAM,WAAW;wBAE1B,UAAU;kEADA;kCAEX;;;;;;kCAGD,8OAAC;kEAAc;;;;;;kCACf,8OAAC;wBACC,UAAU,CAAC,IAAM,WAAW,eAAe,EAAE,MAAM,CAAC,KAAK;wBAEzD,UAAU;wBACV,cAAa;kEAFH;;0CAIV,8OAAC;gCAAO,OAAM;;0CAAG;;;;;;0CACjB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;0CACnB,8OAAC;gCAAO,OAAM;;0CAAK;;;;;;;;;;;;;;;;;;0BAKvB,8OAAC;gBACC,KAAK;gBACL,iBAAiB,CAAC;gBAClB,SAAS;gBACT,yBAAyB;oBAAE,QAAQ;gBAAM;gBAOzC,OAAO;oBACL,iBAAiB,WAAW,YAAY;gBAC1C;gBACA,oBAAkB;0DATP,CAAC;;;;UAIV,EAAE,WAAW,oDAAoD,GAAG;QACtE,CAAC;;;;;;;;;;;;;;;;AAiET;uCAEe", "debugId": null}}, {"offset": {"line": 821, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/components/RichTextEditor.tsx"], "sourcesContent": ["'use client';\n\nimport { useState, useEffect, Component, ReactNode } from 'react';\nimport dynamic from 'next/dynamic';\nimport SimpleRichTextEditor from './SimpleRichTextEditor';\n\n// Error Boundary Component\ninterface ErrorBoundaryProps {\n  children: ReactNode;\n  onError: () => void;\n}\n\ninterface ErrorBoundaryState {\n  hasError: boolean;\n}\n\nclass ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {\n  constructor(props: ErrorBoundaryProps) {\n    super(props);\n    this.state = { hasError: false };\n  }\n\n  static getDerivedStateFromError(): ErrorBoundaryState {\n    return { hasError: true };\n  }\n\n  componentDidCatch(error: Error, errorInfo: any) {\n    console.error('ReactQuill Error:', error, errorInfo);\n    this.props.onError();\n  }\n\n  render() {\n    if (this.state.hasError) {\n      return null; // Let parent handle fallback\n    }\n\n    return this.props.children;\n  }\n}\n\n// Dynamically import ReactQuill to avoid SSR issues\nconst ReactQuill = dynamic(() => import('react-quill'), {\n  ssr: false,\n  loading: () => <div className=\"h-32 bg-gray-100 dark:bg-gray-700 rounded animate-pulse\"></div>\n});\n\ninterface RichTextEditorProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  disabled?: boolean;\n}\n\nconst RichTextEditor: React.FC<RichTextEditorProps> = ({\n  value,\n  onChange,\n  placeholder = 'Enter text...',\n  className = '',\n  disabled = false\n}) => {\n  const [hasError, setHasError] = useState(false);\n  const [isClient, setIsClient] = useState(false);\n\n  useEffect(() => {\n    setIsClient(true);\n  }, []);\n\n  const modules = {\n    toolbar: [\n      [{ 'header': [1, 2, 3, 4, 5, 6, false] }],\n      ['bold', 'italic', 'underline', 'strike'],\n      [{ 'list': 'ordered'}, { 'list': 'bullet' }],\n      [{ 'indent': '-1'}, { 'indent': '+1' }],\n      ['link'],\n      [{ 'align': [] }],\n      ['clean']\n    ],\n  };\n\n  const formats = [\n    'header',\n    'bold', 'italic', 'underline', 'strike',\n    'list', 'bullet', 'indent',\n    'link', 'align'\n  ];\n\n  // If there's an error or we're not on client side, use simple editor\n  if (hasError || !isClient) {\n    return (\n      <SimpleRichTextEditor\n        value={value}\n        onChange={onChange}\n        placeholder={placeholder}\n        className={className}\n        disabled={disabled}\n      />\n    );\n  }\n\n  const handleQuillError = () => {\n    console.warn('ReactQuill error detected, falling back to simple editor');\n    setHasError(true);\n  };\n\n  return (\n    <div className={`rich-text-editor ${className}`}>\n      <div className=\"mb-2\">\n        <button\n          type=\"button\"\n          onClick={() => setHasError(true)}\n          className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n        >\n          Switch to Simple Editor\n        </button>\n      </div>\n      <ErrorBoundary onError={handleQuillError}>\n        <ReactQuill\n          theme=\"snow\"\n          value={value}\n          onChange={onChange}\n          modules={modules}\n          formats={formats}\n          placeholder={placeholder}\n          readOnly={disabled}\n          style={{\n            backgroundColor: disabled ? '#f9fafb' : 'white',\n          }}\n        />\n      </ErrorBoundary>\n      <style jsx global>{`\n        .rich-text-editor .ql-editor {\n          min-height: 150px;\n        }\n        .rich-text-editor .ql-toolbar {\n          border-top: 1px solid #e5e7eb;\n          border-left: 1px solid #e5e7eb;\n          border-right: 1px solid #e5e7eb;\n        }\n        .rich-text-editor .ql-container {\n          border-bottom: 1px solid #e5e7eb;\n          border-left: 1px solid #e5e7eb;\n          border-right: 1px solid #e5e7eb;\n        }\n        .dark .rich-text-editor .ql-toolbar {\n          border-color: #4b5563;\n          background-color: #374151;\n        }\n        .dark .rich-text-editor .ql-container {\n          border-color: #4b5563;\n          background-color: #1f2937;\n        }\n        .dark .rich-text-editor .ql-editor {\n          color: #f9fafb;\n        }\n        .dark .rich-text-editor .ql-toolbar .ql-stroke {\n          stroke: #9ca3af;\n        }\n        .dark .rich-text-editor .ql-toolbar .ql-fill {\n          fill: #9ca3af;\n        }\n      `}</style>\n    </div>\n  );\n};\n\nexport default RichTextEditor;\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;;AAJA;;;;;;AAgBA,MAAM,sBAAsB,qMAAA,CAAA,YAAS;IACnC,YAAY,KAAyB,CAAE;QACrC,KAAK,CAAC;QACN,IAAI,CAAC,KAAK,GAAG;YAAE,UAAU;QAAM;IACjC;IAEA,OAAO,2BAA+C;QACpD,OAAO;YAAE,UAAU;QAAK;IAC1B;IAEA,kBAAkB,KAAY,EAAE,SAAc,EAAE;QAC9C,QAAQ,KAAK,CAAC,qBAAqB,OAAO;QAC1C,IAAI,CAAC,KAAK,CAAC,OAAO;IACpB;IAEA,SAAS;QACP,IAAI,IAAI,CAAC,KAAK,CAAC,QAAQ,EAAE;YACvB,OAAO,MAAM,6BAA6B;QAC5C;QAEA,OAAO,IAAI,CAAC,KAAK,CAAC,QAAQ;IAC5B;AACF;AAEA,oDAAoD;AACpD,MAAM,aAAa,CAAA,GAAA,+JAAA,CAAA,UAAO,AAAD;;;;;;IACvB,KAAK;IACL,SAAS,kBAAM,8OAAC;YAAI,WAAU;;;;;;;AAWhC,MAAM,iBAAgD,CAAC,EACrD,KAAK,EACL,QAAQ,EACR,cAAc,eAAe,EAC7B,YAAY,EAAE,EACd,WAAW,KAAK,EACjB;IACC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEzC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,YAAY;IACd,GAAG,EAAE;IAEL,MAAM,UAAU;QACd,SAAS;YACP;gBAAC;oBAAE,UAAU;wBAAC;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;wBAAG;qBAAM;gBAAC;aAAE;YACzC;gBAAC;gBAAQ;gBAAU;gBAAa;aAAS;YACzC;gBAAC;oBAAE,QAAQ;gBAAS;gBAAG;oBAAE,QAAQ;gBAAS;aAAE;YAC5C;gBAAC;oBAAE,UAAU;gBAAI;gBAAG;oBAAE,UAAU;gBAAK;aAAE;YACvC;gBAAC;aAAO;YACR;gBAAC;oBAAE,SAAS,EAAE;gBAAC;aAAE;YACjB;gBAAC;aAAQ;SACV;IACH;IAEA,MAAM,UAAU;QACd;QACA;QAAQ;QAAU;QAAa;QAC/B;QAAQ;QAAU;QAClB;QAAQ;KACT;IAED,qEAAqE;IACrE,IAAI,YAAY,CAAC,UAAU;QACzB,qBACE,8OAAC,mIAAA,CAAA,UAAoB;YACnB,OAAO;YACP,UAAU;YACV,aAAa;YACb,WAAW;YACX,UAAU;;;;;;IAGhB;IAEA,MAAM,mBAAmB;QACvB,QAAQ,IAAI,CAAC;QACb,YAAY;IACd;IAEA,qBACE,8OAAC;kDAAe,CAAC,iBAAiB,EAAE,WAAW;;0BAC7C,8OAAC;0DAAc;0BACb,cAAA,8OAAC;oBACC,MAAK;oBACL,SAAS,IAAM,YAAY;8DACjB;8BACX;;;;;;;;;;;0BAIH,8OAAC;gBAAc,SAAS;0BACtB,cAAA,8OAAC;oBACC,OAAM;oBACN,OAAO;oBACP,UAAU;oBACV,SAAS;oBACT,SAAS;oBACT,aAAa;oBACb,UAAU;oBACV,OAAO;wBACL,iBAAiB,WAAW,YAAY;oBAC1C;;;;;;;;;;;;;;;;;;;;;AAqCV;uCAEe", "debugId": null}}, {"offset": {"line": 1022, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/source/repos/ravihani/valtics/valtics-ai/app/admin/templates/new/page.tsx"], "sourcesContent": ["'use client';\n\nimport { useEffect, useState } from 'react';\nimport { useAuth } from '@/contexts/AuthContext';\nimport { useRouter } from 'next/navigation';\nimport Link from 'next/link';\nimport { collection, query, where, getDocs, addDoc, Timestamp } from 'firebase/firestore';\nimport { db } from '@/lib/firebase/config';\nimport { Brand } from '@/types';\nimport Navigation from '@/components/Navigation';\nimport RichTextEditor from '@/components/RichTextEditor';\nimport SimpleRichTextEditor from '@/components/SimpleRichTextEditor';\n\ninterface TemplateFormData {\n  brandId: string;\n  solutionProviderName: string;\n  solutionName: string;\n  solutionDescription: string;\n  templateVersion: string;\n  templateVersionDate: string;\n  category: string;\n  price: number;\n}\n\nexport default function NewTemplate() {\n  const { user, loading, isAdmin } = useAuth();\n  const router = useRouter();\n\n  const [brands, setBrands] = useState<Brand[]>([]);\n  const [loadingBrands, setLoadingBrands] = useState(true);\n  const [showCreateBrand, setShowCreateBrand] = useState(false);\n  const [submitting, setSubmitting] = useState(false);\n  const [useSimpleEditor, setUseSimpleEditor] = useState(true); // Default to simple editor for React 19 compatibility\n\n  const [formData, setFormData] = useState<TemplateFormData>({\n    brandId: '',\n    solutionProviderName: '',\n    solutionName: '',\n    solutionDescription: '',\n    templateVersion: '1.0',\n    templateVersionDate: new Date().toISOString().split('T')[0],\n    category: 'Business Analysis',\n    price: 0\n  });\n\n  const [newBrand, setNewBrand] = useState({\n    name: '',\n    description: '',\n    logoUrl: ''\n  });\n\n  useEffect(() => {\n    if (!loading && !isAdmin) {\n      router.push('/');\n      return;\n    }\n    if (isAdmin) {\n      fetchBrands();\n    }\n  }, [loading, isAdmin, router]);\n\n  const fetchBrands = async () => {\n    try {\n      setLoadingBrands(true);\n      const brandsQuery = query(\n        collection(db, 'brands'),\n        where('isActive', '==', true)\n      );\n      const brandsSnapshot = await getDocs(brandsQuery);\n      const brandsData = brandsSnapshot.docs.map(doc => ({\n        id: doc.id,\n        ...doc.data()\n      })) as Brand[];\n      setBrands(brandsData);\n    } catch (error) {\n      console.error('Error fetching brands:', error);\n    } finally {\n      setLoadingBrands(false);\n    }\n  };\n\n  const handleInputChange = (field: keyof TemplateFormData, value: string | number) => {\n    setFormData(prev => ({\n      ...prev,\n      [field]: value\n    }));\n\n    // Auto-populate solution provider name when brand is selected\n    if (field === 'brandId' && value) {\n      const selectedBrand = brands.find(b => b.id === value);\n      if (selectedBrand) {\n        setFormData(prev => ({\n          ...prev,\n          solutionProviderName: selectedBrand.name\n        }));\n      }\n    }\n  };\n\n  const handleCreateBrand = async (e: React.FormEvent) => {\n    e.preventDefault();\n    try {\n      const docRef = await addDoc(collection(db, 'brands'), {\n        ...newBrand,\n        isActive: true,\n        createdAt: Timestamp.now()\n      });\n\n      // Refresh brands list\n      await fetchBrands();\n\n      // Select the newly created brand\n      setFormData(prev => ({\n        ...prev,\n        brandId: docRef.id,\n        solutionProviderName: newBrand.name\n      }));\n\n      // Reset form and close modal\n      setNewBrand({ name: '', description: '', logoUrl: '' });\n      setShowCreateBrand(false);\n\n      alert('Brand created successfully!');\n    } catch (error) {\n      console.error('Error creating brand:', error);\n      alert('Error creating brand. Please try again.');\n    }\n  };\n\n  const generateVersionNumber = () => {\n    const now = new Date();\n    const version = `${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}`;\n    setFormData(prev => ({ ...prev, templateVersion: version }));\n  };\n\n  const handleSubmit = async (e: React.FormEvent) => {\n    e.preventDefault();\n\n    if (!formData.brandId || !formData.solutionName || !formData.solutionDescription) {\n      alert('Please fill in all required fields.');\n      return;\n    }\n\n    try {\n      setSubmitting(true);\n\n      const templateData = {\n        name: formData.solutionName,\n        description: formData.solutionDescription,\n        brandId: formData.brandId,\n        category: formData.category,\n        price: formData.price,\n        solutionProviderName: formData.solutionProviderName,\n        solutionName: formData.solutionName,\n        solutionDescription: formData.solutionDescription,\n        templateVersion: formData.templateVersion,\n        templateVersionDate: Timestamp.fromDate(new Date(formData.templateVersionDate)),\n        status: 'draft' as const,\n        step1Completed: true,\n        step2Completed: false,\n        isActive: false, // Keep inactive until published\n        fileUrls: {},\n        createdAt: Timestamp.now(),\n        updatedAt: Timestamp.now()\n      };\n\n      const docRef = await addDoc(collection(db, 'templates'), templateData);\n\n      // Redirect to step 2\n      router.push(`/admin/templates/${docRef.id}/edit`);\n\n    } catch (error) {\n      console.error('Error creating template:', error);\n      alert('Error creating template. Please try again.');\n    } finally {\n      setSubmitting(false);\n    }\n  };\n\n  if (loading || loadingBrands) {\n    return (\n      <div className=\"flex min-h-screen items-center justify-center\">\n        <div className=\"text-xl\">Loading...</div>\n      </div>\n    );\n  }\n\n  if (!isAdmin) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-gray-50 dark:bg-gray-900\">\n      <Navigation />\n\n      <div className=\"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8\">\n        <div className=\"px-4 py-6 sm:px-0\">\n          {/* Header */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center justify-between\">\n              <div>\n                <h1 className=\"text-3xl font-bold text-gray-900 dark:text-white\">Create New Template</h1>\n                <p className=\"mt-2 text-gray-600 dark:text-gray-300\">\n                  Step 1: Basic Information\n                </p>\n              </div>\n              <Link\n                href=\"/admin\"\n                className=\"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600\"\n              >\n                Back to Admin\n              </Link>\n            </div>\n          </div>\n\n          {/* Progress Indicator */}\n          <div className=\"mb-8\">\n            <div className=\"flex items-center\">\n              <div className=\"flex items-center text-blue-600 dark:text-blue-400\">\n                <div className=\"flex items-center justify-center w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full text-sm font-medium\">\n                  1\n                </div>\n                <span className=\"ml-2 text-sm font-medium\">Basic Information</span>\n              </div>\n              <div className=\"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700\"></div>\n              <div className=\"flex items-center text-gray-400 dark:text-gray-500\">\n                <div className=\"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-sm font-medium\">\n                  2\n                </div>\n                <span className=\"ml-2 text-sm font-medium\">Documents</span>\n              </div>\n            </div>\n          </div>\n\n          {/* Form */}\n          <div className=\"bg-white dark:bg-gray-800 shadow rounded-lg\">\n            <form onSubmit={handleSubmit} className=\"p-6 space-y-6\">\n              {/* Brand Selection */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Company/Brand *\n                </label>\n                <div className=\"flex space-x-3\">\n                  <select\n                    value={formData.brandId}\n                    onChange={(e) => handleInputChange('brandId', e.target.value)}\n                    className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                    required\n                  >\n                    <option value=\"\">Select a brand...</option>\n                    {brands.map(brand => (\n                      <option key={brand.id} value={brand.id}>\n                        {brand.name}\n                      </option>\n                    ))}\n                  </select>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateBrand(true)}\n                    className=\"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 whitespace-nowrap\"\n                  >\n                    Create Brand\n                  </button>\n                </div>\n              </div>\n\n              {/* Solution Provider Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Solution Provider Name *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.solutionProviderName}\n                  onChange={(e) => handleInputChange('solutionProviderName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter solution provider name\"\n                  required\n                />\n              </div>\n\n              {/* Solution Name */}\n              <div>\n                <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                  Solution Name *\n                </label>\n                <input\n                  type=\"text\"\n                  value={formData.solutionName}\n                  onChange={(e) => handleInputChange('solutionName', e.target.value)}\n                  className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                  placeholder=\"Enter solution name\"\n                  required\n                />\n              </div>\n\n              {/* Solution Description */}\n              <div>\n                <div className=\"flex justify-between items-center mb-2\">\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300\">\n                    Solution Description *\n                  </label>\n                  <button\n                    type=\"button\"\n                    onClick={() => setUseSimpleEditor(!useSimpleEditor)}\n                    className=\"text-xs text-blue-600 dark:text-blue-400 hover:underline\"\n                  >\n                    {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}\n                  </button>\n                </div>\n                {useSimpleEditor ? (\n                  <SimpleRichTextEditor\n                    value={formData.solutionDescription}\n                    onChange={(value) => handleInputChange('solutionDescription', value)}\n                    placeholder=\"Enter detailed solution description...\"\n                    className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                  />\n                ) : (\n                  <RichTextEditor\n                    value={formData.solutionDescription}\n                    onChange={(value) => handleInputChange('solutionDescription', value)}\n                    placeholder=\"Enter detailed solution description...\"\n                    className=\"border border-gray-300 dark:border-gray-600 rounded-md\"\n                  />\n                )}\n              </div>\n\n              {/* Template Version and Date */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Template Version *\n                  </label>\n                  <div className=\"flex space-x-2\">\n                    <input\n                      type=\"text\"\n                      value={formData.templateVersion}\n                      onChange={(e) => handleInputChange('templateVersion', e.target.value)}\n                      className=\"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                      placeholder=\"e.g., 1.0\"\n                      required\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={generateVersionNumber}\n                      className=\"bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 whitespace-nowrap\"\n                    >\n                      Auto Generate\n                    </button>\n                  </div>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Template Version Date *\n                  </label>\n                  <input\n                    type=\"date\"\n                    value={formData.templateVersionDate}\n                    onChange={(e) => handleInputChange('templateVersionDate', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                    required\n                  />\n                </div>\n              </div>\n\n              {/* Category and Price */}\n              <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Category\n                  </label>\n                  <select\n                    value={formData.category}\n                    onChange={(e) => handleInputChange('category', e.target.value)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                  >\n                    <option value=\"Business Analysis\">Business Analysis</option>\n                    <option value=\"Financial Planning\">Financial Planning</option>\n                    <option value=\"Technology Assessment\">Technology Assessment</option>\n                    <option value=\"Risk Management\">Risk Management</option>\n                    <option value=\"Strategic Planning\">Strategic Planning</option>\n                  </select>\n                </div>\n\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2\">\n                    Price ($)\n                  </label>\n                  <input\n                    type=\"number\"\n                    min=\"0\"\n                    step=\"0.01\"\n                    value={formData.price}\n                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                    placeholder=\"0.00\"\n                  />\n                </div>\n              </div>\n\n              {/* Submit Button */}\n              <div className=\"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700\">\n                <Link\n                  href=\"/admin\"\n                  className=\"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600\"\n                >\n                  Cancel\n                </Link>\n                <button\n                  type=\"submit\"\n                  disabled={submitting}\n                  className=\"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed\"\n                >\n                  {submitting ? 'Creating...' : 'Continue to Step 2'}\n                </button>\n              </div>\n            </form>\n          </div>\n        </div>\n      </div>\n\n      {/* Create Brand Modal */}\n      {showCreateBrand && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50\">\n          <div className=\"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full\">\n            <div className=\"p-6\">\n              <h3 className=\"text-lg font-medium text-gray-900 dark:text-white mb-4\">Create New Brand</h3>\n              <form onSubmit={handleCreateBrand} className=\"space-y-4\">\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Brand Name *\n                  </label>\n                  <input\n                    type=\"text\"\n                    value={newBrand.name}\n                    onChange={(e) => setNewBrand({ ...newBrand, name: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                    required\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Description\n                  </label>\n                  <textarea\n                    value={newBrand.description}\n                    onChange={(e) => setNewBrand({ ...newBrand, description: e.target.value })}\n                    rows={3}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1\">\n                    Logo URL\n                  </label>\n                  <input\n                    type=\"url\"\n                    value={newBrand.logoUrl}\n                    onChange={(e) => setNewBrand({ ...newBrand, logoUrl: e.target.value })}\n                    className=\"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500\"\n                  />\n                </div>\n                <div className=\"flex space-x-3 pt-4\">\n                  <button\n                    type=\"submit\"\n                    className=\"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800\"\n                  >\n                    Create Brand\n                  </button>\n                  <button\n                    type=\"button\"\n                    onClick={() => setShowCreateBrand(false)}\n                    className=\"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500\"\n                  >\n                    Cancel\n                  </button>\n                </div>\n              </form>\n            </div>\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AACA;AAEA;AACA;AACA;AAXA;;;;;;;;;;;AAwBe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,wHAAA,CAAA,UAAO,AAAD;IACzC,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW,EAAE;IAChD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACnD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE,OAAO,sDAAsD;IAEpH,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAoB;QACzD,SAAS;QACT,sBAAsB;QACtB,cAAc;QACd,qBAAqB;QACrB,iBAAiB;QACjB,qBAAqB,IAAI,OAAO,WAAW,GAAG,KAAK,CAAC,IAAI,CAAC,EAAE;QAC3D,UAAU;QACV,OAAO;IACT;IAEA,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvC,MAAM;QACN,aAAa;QACb,SAAS;IACX;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,WAAW,CAAC,SAAS;YACxB,OAAO,IAAI,CAAC;YACZ;QACF;QACA,IAAI,SAAS;YACX;QACF;IACF,GAAG;QAAC;QAAS;QAAS;KAAO;IAE7B,MAAM,cAAc;QAClB,IAAI;YACF,iBAAiB;YACjB,MAAM,cAAc,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EACtB,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WACf,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;YAE1B,MAAM,iBAAiB,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YACrC,MAAM,aAAa,eAAe,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;oBACjD,IAAI,IAAI,EAAE;oBACV,GAAG,IAAI,IAAI,EAAE;gBACf,CAAC;YACD,UAAU;QACZ,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;QAC1C,SAAU;YACR,iBAAiB;QACnB;IACF;IAEA,MAAM,oBAAoB,CAAC,OAA+B;QACxD,YAAY,CAAA,OAAQ,CAAC;gBACnB,GAAG,IAAI;gBACP,CAAC,MAAM,EAAE;YACX,CAAC;QAED,8DAA8D;QAC9D,IAAI,UAAU,aAAa,OAAO;YAChC,MAAM,gBAAgB,OAAO,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;YAChD,IAAI,eAAe;gBACjB,YAAY,CAAA,OAAQ,CAAC;wBACnB,GAAG,IAAI;wBACP,sBAAsB,cAAc,IAAI;oBAC1C,CAAC;YACH;QACF;IACF;IAEA,MAAM,oBAAoB,OAAO;QAC/B,EAAE,cAAc;QAChB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,WAAW;gBACpD,GAAG,QAAQ;gBACX,UAAU;gBACV,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,sBAAsB;YACtB,MAAM;YAEN,iCAAiC;YACjC,YAAY,CAAA,OAAQ,CAAC;oBACnB,GAAG,IAAI;oBACP,SAAS,OAAO,EAAE;oBAClB,sBAAsB,SAAS,IAAI;gBACrC,CAAC;YAED,6BAA6B;YAC7B,YAAY;gBAAE,MAAM;gBAAI,aAAa;gBAAI,SAAS;YAAG;YACrD,mBAAmB;YAEnB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR;IACF;IAEA,MAAM,wBAAwB;QAC5B,MAAM,MAAM,IAAI;QAChB,MAAM,UAAU,GAAG,IAAI,WAAW,GAAG,CAAC,EAAE,CAAC,IAAI,QAAQ,KAAK,CAAC,EAAE,QAAQ,GAAG,QAAQ,CAAC,GAAG,KAAK,CAAC,EAAE,IAAI,OAAO,GAAG,QAAQ,GAAG,QAAQ,CAAC,GAAG,MAAM;QACvI,YAAY,CAAA,OAAQ,CAAC;gBAAE,GAAG,IAAI;gBAAE,iBAAiB;YAAQ,CAAC;IAC5D;IAEA,MAAM,eAAe,OAAO;QAC1B,EAAE,cAAc;QAEhB,IAAI,CAAC,SAAS,OAAO,IAAI,CAAC,SAAS,YAAY,IAAI,CAAC,SAAS,mBAAmB,EAAE;YAChF,MAAM;YACN;QACF;QAEA,IAAI;YACF,cAAc;YAEd,MAAM,eAAe;gBACnB,MAAM,SAAS,YAAY;gBAC3B,aAAa,SAAS,mBAAmB;gBACzC,SAAS,SAAS,OAAO;gBACzB,UAAU,SAAS,QAAQ;gBAC3B,OAAO,SAAS,KAAK;gBACrB,sBAAsB,SAAS,oBAAoB;gBACnD,cAAc,SAAS,YAAY;gBACnC,qBAAqB,SAAS,mBAAmB;gBACjD,iBAAiB,SAAS,eAAe;gBACzC,qBAAqB,iKAAA,CAAA,YAAS,CAAC,QAAQ,CAAC,IAAI,KAAK,SAAS,mBAAmB;gBAC7E,QAAQ;gBACR,gBAAgB;gBAChB,gBAAgB;gBAChB,UAAU;gBACV,UAAU,CAAC;gBACX,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;gBACxB,WAAW,iKAAA,CAAA,YAAS,CAAC,GAAG;YAC1B;YAEA,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cAAc;YAEzD,qBAAqB;YACrB,OAAO,IAAI,CAAC,CAAC,iBAAiB,EAAE,OAAO,EAAE,CAAC,KAAK,CAAC;QAElD,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,MAAM;QACR,SAAU;YACR,cAAc;QAChB;IACF;IAEA,IAAI,WAAW,eAAe;QAC5B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;0BAAU;;;;;;;;;;;IAG/B;IAEA,IAAI,CAAC,SAAS;QACZ,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,yHAAA,CAAA,UAAU;;;;;0BAEX,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;;0DACC,8OAAC;gDAAG,WAAU;0DAAmD;;;;;;0DACjE,8OAAC;gDAAE,WAAU;0DAAwC;;;;;;;;;;;;kDAIvD,8OAAC,4JAAA,CAAA,UAAI;wCACH,MAAK;wCACL,WAAU;kDACX;;;;;;;;;;;;;;;;;sCAOL,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAAoH;;;;;;0DAGnI,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;kDAE7C,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;0DAA0I;;;;;;0DAGzJ,8OAAC;gDAAK,WAAU;0DAA2B;;;;;;;;;;;;;;;;;;;;;;;sCAMjD,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,UAAU;gCAAc,WAAU;;kDAEtC,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,OAAO,SAAS,OAAO;wDACvB,UAAU,CAAC,IAAM,kBAAkB,WAAW,EAAE,MAAM,CAAC,KAAK;wDAC5D,WAAU;wDACV,QAAQ;;0EAER,8OAAC;gEAAO,OAAM;0EAAG;;;;;;4DAChB,OAAO,GAAG,CAAC,CAAA,sBACV,8OAAC;oEAAsB,OAAO,MAAM,EAAE;8EACnC,MAAM,IAAI;mEADA,MAAM,EAAE;;;;;;;;;;;kEAKzB,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,mBAAmB;wDAClC,WAAU;kEACX;;;;;;;;;;;;;;;;;;kDAOL,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,oBAAoB;gDACpC,UAAU,CAAC,IAAM,kBAAkB,wBAAwB,EAAE,MAAM,CAAC,KAAK;gDACzE,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,YAAY;gDAC5B,UAAU,CAAC,IAAM,kBAAkB,gBAAgB,EAAE,MAAM,CAAC,KAAK;gDACjE,WAAU;gDACV,aAAY;gDACZ,QAAQ;;;;;;;;;;;;kDAKZ,8OAAC;;0DACC,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAM,WAAU;kEAA6D;;;;;;kEAG9E,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,mBAAmB,CAAC;wDACnC,WAAU;kEAET,kBAAkB,wBAAwB;;;;;;;;;;;;4CAG9C,gCACC,8OAAC,mIAAA,CAAA,UAAoB;gDACnB,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,QAAU,kBAAkB,uBAAuB;gDAC9D,aAAY;gDACZ,WAAU;;;;;qEAGZ,8OAAC,6HAAA,CAAA,UAAc;gDACb,OAAO,SAAS,mBAAmB;gDACnC,UAAU,CAAC,QAAU,kBAAkB,uBAAuB;gDAC9D,aAAY;gDACZ,WAAU;;;;;;;;;;;;kDAMhB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEACC,MAAK;gEACL,OAAO,SAAS,eAAe;gEAC/B,UAAU,CAAC,IAAM,kBAAkB,mBAAmB,EAAE,MAAM,CAAC,KAAK;gEACpE,WAAU;gEACV,aAAY;gEACZ,QAAQ;;;;;;0EAEV,8OAAC;gEACC,MAAK;gEACL,SAAS;gEACT,WAAU;0EACX;;;;;;;;;;;;;;;;;;0DAML,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,OAAO,SAAS,mBAAmB;wDACnC,UAAU,CAAC,IAAM,kBAAkB,uBAAuB,EAAE,MAAM,CAAC,KAAK;wDACxE,WAAU;wDACV,QAAQ;;;;;;;;;;;;;;;;;;kDAMd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,OAAO,SAAS,QAAQ;wDACxB,UAAU,CAAC,IAAM,kBAAkB,YAAY,EAAE,MAAM,CAAC,KAAK;wDAC7D,WAAU;;0EAEV,8OAAC;gEAAO,OAAM;0EAAoB;;;;;;0EAClC,8OAAC;gEAAO,OAAM;0EAAqB;;;;;;0EACnC,8OAAC;gEAAO,OAAM;0EAAwB;;;;;;0EACtC,8OAAC;gEAAO,OAAM;0EAAkB;;;;;;0EAChC,8OAAC;gEAAO,OAAM;0EAAqB;;;;;;;;;;;;;;;;;;0DAIvC,8OAAC;;kEACC,8OAAC;wDAAM,WAAU;kEAAkE;;;;;;kEAGnF,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,MAAK;wDACL,OAAO,SAAS,KAAK;wDACrB,UAAU,CAAC,IAAM,kBAAkB,SAAS,WAAW,EAAE,MAAM,CAAC,KAAK,KAAK;wDAC1E,WAAU;wDACV,aAAY;;;;;;;;;;;;;;;;;;kDAMlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,UAAU;gDACV,WAAU;0DAET,aAAa,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;YASzC,iCACC,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAyD;;;;;;0CACvE,8OAAC;gCAAK,UAAU;gCAAmB,WAAU;;kDAC3C,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,IAAI;gDACpB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,MAAM,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACjE,WAAU;gDACV,QAAQ;;;;;;;;;;;;kDAGZ,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,OAAO,SAAS,WAAW;gDAC3B,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,aAAa,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACxE,MAAM;gDACN,WAAU;;;;;;;;;;;;kDAGd,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAkE;;;;;;0DAGnF,8OAAC;gDACC,MAAK;gDACL,OAAO,SAAS,OAAO;gDACvB,UAAU,CAAC,IAAM,YAAY;wDAAE,GAAG,QAAQ;wDAAE,SAAS,EAAE,MAAM,CAAC,KAAK;oDAAC;gDACpE,WAAU;;;;;;;;;;;;kDAGd,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDACC,MAAK;gDACL,WAAU;0DACX;;;;;;0DAGD,8OAAC;gDACC,MAAK;gDACL,SAAS,IAAM,mBAAmB;gDAClC,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}