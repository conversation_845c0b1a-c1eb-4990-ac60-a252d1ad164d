(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[843],{752:(e,r,t)=>{Promise.resolve().then(t.bind(t,2236))},2236:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>b});var a=t(5155),l=t(2115),d=t(3274),s=t(5695),i=t(6874),o=t.n(i),n=t(5317),c=t(1138),g=t(1573),m=t(2547);function b(){let{user:e,loading:r,isAdmin:t}=(0,d.A)(),i=(0,s.useRouter)(),[b,u]=(0,l.useState)([]),[x,h]=(0,l.useState)(!0),[y,p]=(0,l.useState)(!1),[f,k]=(0,l.useState)(!1),[v,j]=(0,l.useState)({brandId:"",solutionProviderName:"",solutionName:"",solutionDescription:"",templateVersion:"1.0",templateVersionDate:new Date().toISOString().split("T")[0],category:"Business Analysis",price:0}),[N,w]=(0,l.useState)({name:"",description:"",logoUrl:""});(0,l.useEffect)(()=>{if(!r&&!t)return void i.push("/");t&&C()},[r,t,i]);let C=async()=>{try{h(!0);let e=(0,n.P)((0,n.rJ)(c.db,"brands"),(0,n._M)("isActive","==",!0)),r=(await (0,n.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));u(r)}catch(e){console.error("Error fetching brands:",e)}finally{h(!1)}},D=(e,r)=>{if(j(t=>({...t,[e]:r})),"brandId"===e&&r){let e=b.find(e=>e.id===r);e&&j(r=>({...r,solutionProviderName:e.name}))}},S=async e=>{e.preventDefault();try{let e=await (0,n.gS)((0,n.rJ)(c.db,"brands"),{...N,isActive:!0,createdAt:n.Dc.now()});await C(),j(r=>({...r,brandId:e.id,solutionProviderName:N.name})),w({name:"",description:"",logoUrl:""}),p(!1),alert("Brand created successfully!")}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},P=async e=>{if(e.preventDefault(),!v.brandId||!v.solutionName||!v.solutionDescription)return void alert("Please fill in all required fields.");try{k(!0);let e={name:v.solutionName,description:v.solutionDescription,brandId:v.brandId,category:v.category,price:v.price,solutionProviderName:v.solutionProviderName,solutionName:v.solutionName,solutionDescription:v.solutionDescription,templateVersion:v.templateVersion,templateVersionDate:n.Dc.fromDate(new Date(v.templateVersionDate)),status:"draft",step1Completed:!0,step2Completed:!1,isActive:!1,fileUrls:{},createdAt:n.Dc.now(),updatedAt:n.Dc.now()},r=await (0,n.gS)((0,n.rJ)(c.db,"templates"),e);i.push("/admin/templates/".concat(r.id,"/edit"))}catch(e){console.error("Error creating template:",e),alert("Error creating template. Please try again.")}finally{k(!1)}};return r||x?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(g.A,{}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create New Template"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Step 1: Basic Information"})]}),(0,a.jsx)(o(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Back to Admin"})]})}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex items-center text-blue-600 dark:text-blue-400",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full text-sm font-medium",children:"1"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Basic Information"})]}),(0,a.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"flex items-center text-gray-400 dark:text-gray-500",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-sm font-medium",children:"2"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Documents"})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:P,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Company/Brand *"}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("select",{value:v.brandId,onChange:e=>D("brandId",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a brand..."}),b.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!0),className:"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 whitespace-nowrap",children:"Create Brand"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Provider Name *"}),(0,a.jsx)("input",{type:"text",value:v.solutionProviderName,onChange:e=>D("solutionProviderName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter solution provider name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Name *"}),(0,a.jsx)("input",{type:"text",value:v.solutionName,onChange:e=>D("solutionName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter solution name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Description *"}),(0,a.jsx)(m.A,{value:v.solutionDescription,onChange:e=>D("solutionDescription",e),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version *"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:v.templateVersion,onChange:e=>D("templateVersion",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 1.0",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>{let e=new Date,r="".concat(e.getFullYear(),".").concat((e.getMonth()+1).toString().padStart(2,"0"),".").concat(e.getDate().toString().padStart(2,"0"));j(e=>({...e,templateVersion:r}))},className:"bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 whitespace-nowrap",children:"Auto Generate"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version Date *"}),(0,a.jsx)("input",{type:"date",value:v.templateVersionDate,onChange:e=>D("templateVersionDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:v.category,onChange:e=>D("category",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"Business Analysis",children:"Business Analysis"}),(0,a.jsx)("option",{value:"Financial Planning",children:"Financial Planning"}),(0,a.jsx)("option",{value:"Technology Assessment",children:"Technology Assessment"}),(0,a.jsx)("option",{value:"Risk Management",children:"Risk Management"}),(0,a.jsx)("option",{value:"Strategic Planning",children:"Strategic Planning"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Price ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:v.price,onChange:e=>D("price",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)(o(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:f,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed",children:f?"Creating...":"Continue to Step 2"})]})]})})]})}),y&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Create New Brand"}),(0,a.jsxs)("form",{onSubmit:S,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Brand Name *"}),(0,a.jsx)("input",{type:"text",value:N.name,onChange:e=>w({...N,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:N.description,onChange:e=>w({...N,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:N.logoUrl,onChange:e=>w({...N,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Create Brand"}),(0,a.jsx)("button",{type:"button",onClick:()=>p(!1),className:"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Cancel"})]})]})]})})})]}):null}},2547:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(5155),l=t(9137),d=t.n(l),s=t(2115),i=t(5028);t(4098);let o=(0,i.default)(()=>Promise.all([t.e(553),t.e(981)]).then(t.t.bind(t,1981,23)),{loadableGenerated:{webpack:()=>[1981]},ssr:!1}),n=e=>{let{value:r,onChange:t,placeholder:l="Enter text...",className:i="",disabled:n=!1}=e,c=(0,s.useRef)(null),g={toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],["link"],[{align:[]}],["clean"]]};return(0,a.jsxs)("div",{className:"jsx-3979297ce4da0409 "+"rich-text-editor ".concat(i),children:[(0,a.jsx)(o,{ref:c,theme:"snow",value:r,onChange:t,modules:g,formats:["header","bold","italic","underline","strike","list","bullet","indent","link","align"],placeholder:l,readOnly:n,style:{backgroundColor:n?"#f9fafb":"white"}}),(0,a.jsx)(d(),{id:"3979297ce4da0409",children:".rich-text-editor .ql-editor{min-height:150px}.rich-text-editor .ql-toolbar{border-top:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.rich-text-editor .ql-container{border-bottom:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.dark .rich-text-editor .ql-toolbar{border-color:#4b5563;background-color:#374151}.dark .rich-text-editor .ql-container{border-color:#4b5563;background-color:#1f2937}.dark .rich-text-editor .ql-editor{color:#f9fafb}.dark .rich-text-editor .ql-toolbar .ql-stroke{stroke:#9ca3af}.dark .rich-text-editor .ql-toolbar .ql-fill{fill:#9ca3af}"})]})}}},e=>{var r=r=>e(e.s=r);e.O(0,[723,992,965,288,874,63,515,573,441,684,358],()=>r(752)),_N_E=e.O()}]);