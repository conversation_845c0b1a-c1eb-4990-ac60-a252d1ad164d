'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { collection, query, where, getDocs, addDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Brand } from '@/types';
import Navigation from '@/components/Navigation';
import RichTextEditor from '@/components/RichTextEditor';
import SimpleRichTextEditor from '@/components/SimpleRichTextEditor';

interface TemplateFormData {
  brandId: string;
  solutionProviderName: string;
  solutionName: string;
  solutionDescription: string;
  templateVersion: string;
  templateVersionDate: string;
  category: string;
  price: number;
}

export default function NewTemplate() {
  const { user, loading, isAdmin } = useAuth();
  const router = useRouter();

  const [brands, setBrands] = useState<Brand[]>([]);
  const [loadingBrands, setLoadingBrands] = useState(true);
  const [showCreateBrand, setShowCreateBrand] = useState(false);
  const [submitting, setSubmitting] = useState(false);
  const [useSimpleEditor, setUseSimpleEditor] = useState(true); // Default to simple editor for React 19 compatibility

  const [formData, setFormData] = useState<TemplateFormData>({
    brandId: '',
    solutionProviderName: '',
    solutionName: '',
    solutionDescription: '',
    templateVersion: '1.0',
    templateVersionDate: new Date().toISOString().split('T')[0],
    category: 'Business Analysis',
    price: 0
  });

  const [newBrand, setNewBrand] = useState({
    name: '',
    description: '',
    logoUrl: ''
  });

  useEffect(() => {
    if (!loading && !isAdmin) {
      router.push('/');
      return;
    }
    if (isAdmin) {
      fetchBrands();
    }
  }, [loading, isAdmin, router]);

  const fetchBrands = async () => {
    try {
      setLoadingBrands(true);
      const brandsQuery = query(
        collection(db, 'brands'),
        where('isActive', '==', true)
      );
      const brandsSnapshot = await getDocs(brandsQuery);
      const brandsData = brandsSnapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as Brand[];
      setBrands(brandsData);
    } catch (error) {
      console.error('Error fetching brands:', error);
    } finally {
      setLoadingBrands(false);
    }
  };

  const handleInputChange = (field: keyof TemplateFormData, value: string | number) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));

    // Auto-populate solution provider name when brand is selected
    if (field === 'brandId' && value) {
      const selectedBrand = brands.find(b => b.id === value);
      if (selectedBrand) {
        setFormData(prev => ({
          ...prev,
          solutionProviderName: selectedBrand.name
        }));
      }
    }
  };

  const handleCreateBrand = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const docRef = await addDoc(collection(db, 'brands'), {
        ...newBrand,
        isActive: true,
        createdAt: Timestamp.now()
      });

      // Refresh brands list
      await fetchBrands();

      // Select the newly created brand
      setFormData(prev => ({
        ...prev,
        brandId: docRef.id,
        solutionProviderName: newBrand.name
      }));

      // Reset form and close modal
      setNewBrand({ name: '', description: '', logoUrl: '' });
      setShowCreateBrand(false);

      alert('Brand created successfully!');
    } catch (error) {
      console.error('Error creating brand:', error);
      alert('Error creating brand. Please try again.');
    }
  };

  const generateVersionNumber = () => {
    const now = new Date();
    const version = `${now.getFullYear()}.${(now.getMonth() + 1).toString().padStart(2, '0')}.${now.getDate().toString().padStart(2, '0')}`;
    setFormData(prev => ({ ...prev, templateVersion: version }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!formData.brandId || !formData.solutionName || !formData.solutionDescription) {
      alert('Please fill in all required fields.');
      return;
    }

    try {
      setSubmitting(true);

      const templateData = {
        name: formData.solutionName,
        description: formData.solutionDescription,
        brandId: formData.brandId,
        category: formData.category,
        price: formData.price,
        solutionProviderName: formData.solutionProviderName,
        solutionName: formData.solutionName,
        solutionDescription: formData.solutionDescription,
        templateVersion: formData.templateVersion,
        templateVersionDate: Timestamp.fromDate(new Date(formData.templateVersionDate)),
        status: 'draft' as const,
        step1Completed: true,
        step2Completed: false,
        isActive: false, // Keep inactive until published
        fileUrls: {},
        createdAt: Timestamp.now(),
        updatedAt: Timestamp.now()
      };

      const docRef = await addDoc(collection(db, 'templates'), templateData);

      // Redirect to step 2
      router.push(`/admin/templates/${docRef.id}/edit`);

    } catch (error) {
      console.error('Error creating template:', error);
      alert('Error creating template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading || loadingBrands) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!isAdmin) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />

      <div className="max-w-4xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Create New Template</h1>
                <p className="mt-2 text-gray-600 dark:text-gray-300">
                  Step 1: Basic Information
                </p>
              </div>
              <Link
                href="/admin"
                className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Back to Admin
              </Link>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center">
              <div className="flex items-center text-blue-600 dark:text-blue-400">
                <div className="flex items-center justify-center w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full text-sm font-medium">
                  1
                </div>
                <span className="ml-2 text-sm font-medium">Basic Information</span>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"></div>
              <div className="flex items-center text-gray-400 dark:text-gray-500">
                <div className="flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-sm font-medium">
                  2
                </div>
                <span className="ml-2 text-sm font-medium">Documents</span>
              </div>
            </div>
          </div>

          {/* Form */}
          <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Brand Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Company/Brand *
                </label>
                <div className="flex space-x-3">
                  <select
                    value={formData.brandId}
                    onChange={(e) => handleInputChange('brandId', e.target.value)}
                    className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    required
                  >
                    <option value="">Select a brand...</option>
                    {brands.map(brand => (
                      <option key={brand.id} value={brand.id}>
                        {brand.name}
                      </option>
                    ))}
                  </select>
                  <button
                    type="button"
                    onClick={() => setShowCreateBrand(true)}
                    className="bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 whitespace-nowrap"
                  >
                    Create Brand
                  </button>
                </div>
              </div>

              {/* Solution Provider Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Solution Provider Name *
                </label>
                <input
                  type="text"
                  value={formData.solutionProviderName}
                  onChange={(e) => handleInputChange('solutionProviderName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter solution provider name"
                  required
                />
              </div>

              {/* Solution Name */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Solution Name *
                </label>
                <input
                  type="text"
                  value={formData.solutionName}
                  onChange={(e) => handleInputChange('solutionName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  placeholder="Enter solution name"
                  required
                />
              </div>

              {/* Solution Description */}
              <div>
                <div className="flex justify-between items-center mb-2">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                    Solution Description *
                  </label>
                  <button
                    type="button"
                    onClick={() => setUseSimpleEditor(!useSimpleEditor)}
                    className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                  >
                    {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}
                  </button>
                </div>
                {useSimpleEditor ? (
                  <SimpleRichTextEditor
                    value={formData.solutionDescription}
                    onChange={(value) => handleInputChange('solutionDescription', value)}
                    placeholder="Enter detailed solution description..."
                    className="border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                ) : (
                  <RichTextEditor
                    value={formData.solutionDescription}
                    onChange={(value) => handleInputChange('solutionDescription', value)}
                    placeholder="Enter detailed solution description..."
                    className="border border-gray-300 dark:border-gray-600 rounded-md"
                  />
                )}
              </div>

              {/* Template Version and Date */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Template Version *
                  </label>
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={formData.templateVersion}
                      onChange={(e) => handleInputChange('templateVersion', e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      placeholder="e.g., 1.0"
                      required
                    />
                    <button
                      type="button"
                      onClick={generateVersionNumber}
                      className="bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 whitespace-nowrap"
                    >
                      Auto Generate
                    </button>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Template Version Date *
                  </label>
                  <input
                    type="date"
                    value={formData.templateVersionDate}
                    onChange={(e) => handleInputChange('templateVersionDate', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
              </div>

              {/* Category and Price */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Category
                  </label>
                  <select
                    value={formData.category}
                    onChange={(e) => handleInputChange('category', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  >
                    <option value="Business Analysis">Business Analysis</option>
                    <option value="Financial Planning">Financial Planning</option>
                    <option value="Technology Assessment">Technology Assessment</option>
                    <option value="Risk Management">Risk Management</option>
                    <option value="Strategic Planning">Strategic Planning</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Price ($)
                  </label>
                  <input
                    type="number"
                    min="0"
                    step="0.01"
                    value={formData.price}
                    onChange={(e) => handleInputChange('price', parseFloat(e.target.value) || 0)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    placeholder="0.00"
                  />
                </div>
              </div>

              {/* Submit Button */}
              <div className="flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700">
                <Link
                  href="/admin"
                  className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600"
                >
                  Cancel
                </Link>
                <button
                  type="submit"
                  disabled={submitting}
                  className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {submitting ? 'Creating...' : 'Continue to Step 2'}
                </button>
              </div>
            </form>
          </div>
        </div>
      </div>

      {/* Create Brand Modal */}
      {showCreateBrand && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full">
            <div className="p-6">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Create New Brand</h3>
              <form onSubmit={handleCreateBrand} className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Brand Name *
                  </label>
                  <input
                    type="text"
                    value={newBrand.name}
                    onChange={(e) => setNewBrand({ ...newBrand, name: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Description
                  </label>
                  <textarea
                    value={newBrand.description}
                    onChange={(e) => setNewBrand({ ...newBrand, description: e.target.value })}
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Logo URL
                  </label>
                  <input
                    type="url"
                    value={newBrand.logoUrl}
                    onChange={(e) => setNewBrand({ ...newBrand, logoUrl: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex space-x-3 pt-4">
                  <button
                    type="submit"
                    className="flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800"
                  >
                    Create Brand
                  </button>
                  <button
                    type="button"
                    onClick={() => setShowCreateBrand(false)}
                    className="flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500"
                  >
                    Cancel
                  </button>
                </div>
              </form>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
