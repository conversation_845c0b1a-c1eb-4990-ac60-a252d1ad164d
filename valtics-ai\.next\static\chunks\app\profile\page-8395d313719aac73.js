(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[636],{3581:(e,t,a)=>{Promise.resolve().then(a.bind(a,8591))},8591:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>g});var r=a(5155),s=a(2115),d=a(3274),l=a(5695),i=a(6874),n=a.n(i),x=a(5317),c=a(1138),m=a(1573);function g(){let{user:e,firebaseUser:t,loading:a,logOut:i}=(0,d.A)(),g=(0,l.useRouter)(),[o,b]=(0,s.useState)(null),[u,h]=(0,s.useState)(null),[y,p]=(0,s.useState)([]),[k,j]=(0,s.useState)(!0),[N,v]=(0,s.useState)("profile"),[f,w]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a||e||g.push("/login")},[e,a,g]),(0,s.useEffect)(()=>{e&&t&&S()},[e,t]);let S=async()=>{if(t)try{j(!0);let e=await (0,x.x7)((0,x.H9)(c.db,"users",t.uid));e.exists()&&b({id:e.id,...e.data()});let a=(0,x.P)((0,x.rJ)(c.db,"subscriptions"),(0,x._M)("userId","==",t.uid)),r=await (0,x.GG)(a);if(!r.empty){let e=r.docs[0];h({id:e.id,...e.data()})}let s=(0,x.P)((0,x.rJ)(c.db,"bvaInstances"),(0,x._M)("userId","==",t.uid),(0,x.My)("updatedAt","desc")),d=(await (0,x.GG)(s)).docs.map(e=>({id:e.id,...e.data()}));p(d)}catch(e){console.error("Error fetching profile data:",e)}finally{j(!1)}},A=async()=>{try{await i(),g.push("/")}catch(e){console.error("Error logging out:",e)}};if(a||k)return(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})});if(!e||!o)return null;let D=e=>{switch(e){case"completed":return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";case"in-progress":return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}};return(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(m.A,{title:"VALTICS AI",showBackButton:!0}),(0,r.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Profile & Settings"}),(0,r.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Manage your account settings and view your activity."})]}),(0,r.jsx)("div",{className:"mb-8",children:(0,r.jsx)("nav",{className:"flex space-x-8",children:[{id:"profile",name:"Profile"},{id:"subscription",name:"Subscription"},{id:"bvas",name:"My BVAs"}].map(e=>(0,r.jsx)("button",{onClick:()=>v(e.id),className:"py-2 px-1 border-b-2 font-medium text-sm ".concat(N===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"),children:e.name},e.id))})}),"profile"===N&&(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Information"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,r.jsx)("input",{type:"email",value:(null==t?void 0:t.email)||"",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"}),(0,r.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Email cannot be changed"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Account Type"}),(0,r.jsx)("input",{type:"text",value:"admin"===o.role?"Administrator":"Standard User",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Member Since"}),(0,r.jsx)("input",{type:"text",value:new Date(o.createdAt).toLocaleDateString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Total BVAs Created"}),(0,r.jsx)("input",{type:"text",value:y.length.toString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]})]})]})}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Actions"}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Change Password"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your account password"})]}),(0,r.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Change Password"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Download Data"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Export all your BVA data"})]}),(0,r.jsx)("button",{className:"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800",children:"Export Data"})]}),(0,r.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 dark:border-red-600 rounded-lg",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-red-900 dark:text-red-400",children:"Sign Out"}),(0,r.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:"Sign out of your account"})]}),(0,r.jsx)("button",{onClick:A,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Sign Out"})]})]})]})})]}),"subscription"===N&&(0,r.jsx)("div",{className:"space-y-6",children:u?(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Current Subscription"}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Plan Type"}),(0,r.jsx)("div",{className:"flex items-center",children:(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("enterprise"===u.type?"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200":"premium"===u.type?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"),children:u.type.charAt(0).toUpperCase()+u.type.slice(1)})})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),(0,r.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat("active"===u.status?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"cancelled"===u.status?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"),children:u.status.charAt(0).toUpperCase()+u.status.slice(1)})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Start Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white",children:new Date(u.startDate).toLocaleDateString()})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"End Date"}),(0,r.jsx)("p",{className:"text-sm text-gray-900 dark:text-white",children:new Date(u.endDate).toLocaleDateString()})]}),(0,r.jsxs)("div",{className:"md:col-span-2",children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Access"}),(0,r.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white",children:[u.accessibleTemplateIds.length," templates available"]})]})]}),(0,r.jsxs)("div",{className:"mt-6 flex space-x-4",children:[(0,r.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Upgrade Plan"}),(0,r.jsx)("button",{className:"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Manage Billing"})]})]})}):(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-6xl mb-4",children:"\uD83D\uDCB3"}),(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Active Subscription"}),(0,r.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Subscribe to access premium templates and features."}),(0,r.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"View Subscription Plans"})]})})}),"bvas"===N&&(0,r.jsx)("div",{className:"space-y-6",children:(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Your BVAs"}),y.length>0?(0,r.jsx)("div",{className:"space-y-4",children:y.map(e=>(0,r.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,r.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Client: ",e.clientName]}),(0,r.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500",children:["Last updated: ",new Date(e.updatedAt).toLocaleDateString()]})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,r.jsx)("span",{className:"px-2 py-1 text-xs font-medium rounded-full ".concat(D(e.status)),children:e.status}),(0,r.jsx)(n(),{href:"/bva/".concat(e.id),className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View"})]})]})},e.id))}):(0,r.jsxs)("div",{className:"text-center py-8",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,r.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No BVAs created yet."}),(0,r.jsx)(n(),{href:"/bva/new",className:"mt-4 inline-block bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Create Your First BVA"})]})]})})})]})})]})}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,573,441,684,358],()=>t(3581)),_N_E=e.O()}]);