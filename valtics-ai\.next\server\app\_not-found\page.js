(()=>{var e={};e.id=492,e.ids=[492],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>i});var s=r(12907);(0,s.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,s.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>o,ThemeProvider:()=>a});var s=r(60687),i=r(43210);let n=(0,i.createContext)(null),o=()=>(0,i.useContext)(n),a=({children:e})=>{let[t,r]=(0,i.useState)("light"),[o,a]=(0,i.useState)(!1);(0,i.useEffect)(()=>{a(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return o?(0,s.jsx)(n.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,s.jsx)(s.Fragment,{children:e})}},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>d});var s=r(60687),i=r(43210),n=r(19978),o=r(56304),a=r(75535),l=r(53836);let u=(0,i.createContext)(null),d=({children:e})=>{let[t,r]=(0,i.useState)(null),[d,c]=(0,i.useState)(null),[p,m]=(0,i.useState)(!0),[h,v]=(0,i.useState)(!1),[x,f]=(0,i.useState)(!1),[b,g]=(0,i.useState)(!1),P=async()=>{if(d)try{let e=await (0,a.x7)((0,a.H9)(o.db,"users",d.uid));if(e.exists()){let t={id:e.id,...e.data()},s=(0,l.gZ)(t);s&&!t.trialExpired&&(await (0,a.mZ)((0,a.H9)(o.db,"users",d.uid),{trialExpired:!0,updatedAt:new Date}),t.trialExpired=!0),r(t),v("admin"===t.role),f((0,l.nE)(t)),g(s)}}catch(e){console.error("Error refreshing user data:",e)}};(0,i.useEffect)(()=>{let e=(0,n.hg)(o.j2,async e=>{c(e),e?await P():(r(null),v(!1),f(!1),g(!1)),m(!1)});return()=>e()},[]),(0,i.useEffect)(()=>{d&&P()},[d]);let w=async(e,t)=>{await (0,n.x9)(o.j2,e,t)},y=async(e,t)=>{let r=await (0,n.eJ)(o.j2,e,t),s=(0,l.ow)(r.user.email||e);await (0,a.BN)((0,a.H9)(o.db,"users",r.user.uid),s)},C=async()=>{await (0,n.CI)(o.j2)},A=async()=>{let e=new n.HF,t=await (0,n.df)(o.j2,e);if(!(await (0,a.x7)((0,a.H9)(o.db,"users",t.user.uid))).exists()){let e=(0,l.ow)(t.user.email||"",{firstName:t.user.displayName?.split(" ")[0],lastName:t.user.displayName?.split(" ").slice(1).join(" ")});await (0,a.BN)((0,a.H9)(o.db,"users",t.user.uid),e)}},E=async()=>{let e=new n.sk,t=await (0,n.df)(o.j2,e);if(!(await (0,a.x7)((0,a.H9)(o.db,"users",t.user.uid))).exists()){let e=(0,l.ow)(t.user.email||"",{firstName:t.user.displayName?.split(" ")[0],lastName:t.user.displayName?.split(" ").slice(1).join(" ")});await (0,a.BN)((0,a.H9)(o.db,"users",t.user.uid),e)}};return(0,s.jsx)(u.Provider,{value:{user:t,firebaseUser:d,loading:p,signIn:w,signUp:y,logOut:C,signInWithGoogle:A,signInWithFacebook:E,isAdmin:h,canAccessPremiumFeatures:x,isTrialExpired:b,refreshUserData:P},children:e})},c=()=>{let e=(0,i.useContext)(u);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},53836:(e,t,r)=>{"use strict";function s(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function i(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||s(e)))}function n(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let t=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let t=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-t.getTime())/864e5))}(e);return t<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:t<=3?{message:`Your trial expires in ${t} day${1===t?"":"s"}. Upgrade now to continue.`,type:"warning",daysRemaining:t}:{message:`Your trial expires in ${t} days on ${!e.trialEndDate?"":new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}.`,type:"info",daysRemaining:t}}function o(e,t={}){return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,t=new Date(e);return t.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:t,trialExpired:!1}}(),...t}}r.d(t,{Mo:()=>n,gZ:()=>s,nE:()=>i,ow:()=>o})},55511:e=>{"use strict";e.exports=require("crypto")},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>u,j2:()=>l});var s=r(67989),i=r(19978),n=r(75535),o=r(70146);let a=(0,s.Dk)().length?(0,s.Sx)():(0,s.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,i.xI)(a),u=(0,n.aU)(a);(0,o.c7)(a)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,metadata:()=>l});var s=r(37413),i=r(61421),n=r.n(i);r(82704);var o=r(94442),a=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function u({children:e}){return(0,s.jsx)("html",{lang:"en",children:(0,s.jsx)("body",{className:n().className,children:(0,s.jsx)(a.ThemeProvider,{children:(0,s.jsx)(o.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77182:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var s=r(65239),i=r(48088),n=r(88170),o=r.n(n),a=r(30893),l={};for(let e in a)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let u={children:["",{children:["/_not-found",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=[],c={require:r,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/_not-found/page",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},82704:()=>{},91645:e=>{"use strict";e.exports=require("net")},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>i});var s=r(12907);let i=(0,s.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,s.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[447,823],()=>r(77182));module.exports=s})();