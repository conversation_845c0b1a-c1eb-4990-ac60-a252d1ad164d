(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[943],{1469:(e,t,a)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var a in t)Object.defineProperty(e,a,{enumerable:!0,get:t[a]})}(t,{default:function(){return n},getImageProps:function(){return i}});let r=a(8229),s=a(8883),l=a(3063),d=r._(a(1193));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:d.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(t))void 0===a&&delete t[e];return{props:t}}let n=l.Image},5383:(e,t,a)=>{Promise.resolve().then(a.bind(a,8088))},5695:(e,t,a)=>{"use strict";var r=a(8999);a.o(r,"useParams")&&a.d(t,{useParams:function(){return r.useParams}}),a.o(r,"useRouter")&&a.d(t,{useRouter:function(){return r.useRouter}}),a.o(r,"useSearchParams")&&a.d(t,{useSearchParams:function(){return r.useSearchParams}})},6766:(e,t,a)=>{"use strict";a.d(t,{default:()=>s.a});var r=a(1469),s=a.n(r)},8088:(e,t,a)=>{"use strict";a.r(t),a.d(t,{default:()=>x});var r=a(5155),s=a(2115),l=a(3274),d=a(5695),i=a(5317),n=a(1138),c=a(1573),o=a(9729);function m(){let{user:e,firebaseUser:t,loading:a}=(0,l.A)(),o=(0,d.useRouter)(),m=(0,d.useSearchParams)().get("template"),[x,u]=(0,s.useState)(null),[g,h]=(0,s.useState)(null),[b,y]=(0,s.useState)(!0),[f,j]=(0,s.useState)(1),[p,v]=(0,s.useState)({clientName:"",projectName:"",industry:"",currentCosts:0,expectedBenefits:0,timeframe:12,additionalData:{}}),[N,k]=(0,s.useState)(!1);(0,s.useEffect)(()=>{a||e||o.push("/login")},[e,a,o]),(0,s.useEffect)(()=>{e&&m?w():e&&!m&&y(!1)},[e,m]);let w=async()=>{try{if(y(!0),m){let e=await (0,i.x7)((0,i.H9)(n.db,"templates",m));if(e.exists()){let t={id:e.id,...e.data()};u(t);let a=await (0,i.x7)((0,i.H9)(n.db,"brands",t.brandId));a.exists()&&h({id:a.id,...a.data()})}}}catch(e){console.error("Error fetching template data:",e)}finally{y(!1)}},C=(e,t)=>{v(a=>({...a,[e]:t}))},A=async function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"draft";if(t)try{k(!0);let a={userId:t.uid,templateId:m||"",name:p.projectName||"Untitled BVA",clientName:p.clientName,status:e,data:p,createdAt:new Date,updatedAt:new Date},r=await (0,i.gS)((0,i.rJ)(n.db,"bvaInstances"),a);o.push("/bva/".concat(r.id))}catch(e){console.error("Error saving BVA:",e),alert("Error saving BVA. Please try again.")}finally{k(!1)}};return a||b?(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,r.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})}):e?(0,r.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,r.jsx)(c.A,{title:"VALTICS AI",showBackButton:!0}),(0,r.jsx)("div",{className:"max-w-3xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,r.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create New BVA"}),x&&g&&(0,r.jsxs)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:["Using template: ",(0,r.jsx)("span",{className:"font-medium",children:x.name})," by ",g.name]})]}),(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("div",{className:"flex items-center",children:Array.from({length:4},(e,t)=>(0,r.jsxs)("div",{className:"flex items-center",children:[(0,r.jsx)("div",{className:"w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ".concat(t+1<=f?"bg-blue-600 dark:bg-blue-700 text-white":"bg-gray-200 dark:bg-gray-600 text-gray-600 dark:text-gray-300"),children:t+1}),t<3&&(0,r.jsx)("div",{className:"w-16 h-1 mx-2 ".concat(t+1<f?"bg-blue-600 dark:bg-blue-700":"bg-gray-200 dark:bg-gray-600")})]},t))}),(0,r.jsxs)("div",{className:"flex justify-between mt-2 text-sm text-gray-600 dark:text-gray-400",children:[(0,r.jsx)("span",{children:"Project Info"}),(0,r.jsx)("span",{children:"Financial"}),(0,r.jsx)("span",{children:"Details"}),(0,r.jsx)("span",{children:"Review"})]})]}),(0,r.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-md p-8 mb-8",children:(()=>{switch(f){case 1:return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Project Information"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Project Name *"}),(0,r.jsx)("input",{type:"text",value:p.projectName,onChange:e=>C("projectName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter project name",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Client Name *"}),(0,r.jsx)("input",{type:"text",value:p.clientName,onChange:e=>C("clientName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter client name",required:!0})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Industry"}),(0,r.jsxs)("select",{value:p.industry,onChange:e=>C("industry",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:"",children:"Select industry"}),(0,r.jsx)("option",{value:"Technology",children:"Technology"}),(0,r.jsx)("option",{value:"Healthcare",children:"Healthcare"}),(0,r.jsx)("option",{value:"Finance",children:"Finance"}),(0,r.jsx)("option",{value:"Manufacturing",children:"Manufacturing"}),(0,r.jsx)("option",{value:"Retail",children:"Retail"}),(0,r.jsx)("option",{value:"Education",children:"Education"}),(0,r.jsx)("option",{value:"Government",children:"Government"}),(0,r.jsx)("option",{value:"Other",children:"Other"})]})]})]});case 2:return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Financial Information"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Current Annual Costs ($)"}),(0,r.jsx)("input",{type:"number",value:p.currentCosts,onChange:e=>C("currentCosts",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",placeholder:"0",min:"0"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Current annual costs related to this solution area"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Expected Annual Benefits ($)"}),(0,r.jsx)("input",{type:"number",value:p.expectedBenefits,onChange:e=>C("expectedBenefits",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",placeholder:"0",min:"0"}),(0,r.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:"Expected annual benefits from the new solution"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Analysis Timeframe (months)"}),(0,r.jsxs)("select",{value:p.timeframe,onChange:e=>C("timeframe",parseInt(e.target.value)),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",children:[(0,r.jsx)("option",{value:12,children:"12 months"}),(0,r.jsx)("option",{value:24,children:"24 months"}),(0,r.jsx)("option",{value:36,children:"36 months"}),(0,r.jsx)("option",{value:48,children:"48 months"}),(0,r.jsx)("option",{value:60,children:"60 months"})]})]})]});case 3:return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Additional Details"}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Key Business Drivers"}),(0,r.jsx)("textarea",{value:p.additionalData.businessDrivers||"",onChange:e=>C("additionalData",{...p.additionalData,businessDrivers:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",placeholder:"Describe the key business drivers for this project..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Success Metrics"}),(0,r.jsx)("textarea",{value:p.additionalData.successMetrics||"",onChange:e=>C("additionalData",{...p.additionalData,successMetrics:e.target.value}),rows:4,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",placeholder:"Define how success will be measured..."})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-2",children:"Implementation Timeline"}),(0,r.jsx)("input",{type:"text",value:p.additionalData.timeline||"",onChange:e=>C("additionalData",{...p.additionalData,timeline:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 6 months implementation, 3 months rollout"})]})]});case 4:return(0,r.jsxs)("div",{className:"space-y-6",children:[(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Review & Save"}),(0,r.jsxs)("div",{className:"bg-gray-50 rounded-lg p-6",children:[(0,r.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"BVA Summary"}),(0,r.jsxs)("dl",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Project Name"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:p.projectName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Client"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:p.clientName})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Industry"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:p.industry||"Not specified"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Timeframe"}),(0,r.jsxs)("dd",{className:"text-sm text-gray-900",children:[p.timeframe," months"]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Current Costs"}),(0,r.jsxs)("dd",{className:"text-sm text-gray-900",children:["$",p.currentCosts.toLocaleString()]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Expected Benefits"}),(0,r.jsxs)("dd",{className:"text-sm text-gray-900",children:["$",p.expectedBenefits.toLocaleString()]})]})]}),x&&(0,r.jsxs)("div",{className:"mt-4 pt-4 border-t",children:[(0,r.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Template"}),(0,r.jsx)("dd",{className:"text-sm text-gray-900",children:x.name})]})]}),(0,r.jsxs)("div",{className:"flex space-x-4",children:[(0,r.jsx)("button",{onClick:()=>A("draft"),disabled:N,className:"flex-1 bg-gray-600 text-white px-6 py-3 rounded-md font-medium hover:bg-gray-700 disabled:opacity-50",children:N?"Saving...":"Save as Draft"}),(0,r.jsx)("button",{onClick:()=>A("in-progress"),disabled:N,className:"flex-1 bg-blue-600 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50",children:N?"Saving...":"Save & Continue"})]})]});default:return null}})()}),f<4&&(0,r.jsxs)("div",{className:"flex justify-between",children:[(0,r.jsx)("button",{onClick:()=>{f>1&&j(f-1)},disabled:1===f,className:"px-6 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed",children:"Previous"}),(0,r.jsx)("button",{onClick:()=>{f<4&&j(f+1)},disabled:1===f&&(!p.projectName||!p.clientName)||N,className:"px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed",children:"Next"})]})]})})]}):null}function x(){return(0,r.jsx)(s.Suspense,{fallback:(0,r.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,r.jsx)("div",{className:"text-xl",children:"Loading..."})}),children:(0,r.jsx)(o.Ay,{feature:"BVA Creation",children:(0,r.jsx)(m,{})})})}},9729:(e,t,a)=>{"use strict";a.d(t,{Ay:()=>i,eF:()=>n});var r=a(5155),s=a(6874),l=a.n(s),d=a(3274);function i(e){let{children:t,feature:a,showUpgrade:s=!0}=e,{user:i,canAccessPremiumFeatures:n}=(0,d.A)();return n?(0,r.jsx)(r.Fragment,{children:t}):i?(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center",children:[(0,r.jsx)("div",{className:"text-red-400 dark:text-red-300 text-6xl mb-4",children:"⏰"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Trial Expired"}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:["Your free trial has ended. Upgrade to continue using ",a," and other premium features."]}),(0,r.jsxs)("div",{className:"bg-gray-50 dark:bg-gray-700 rounded-lg p-4 mb-6",children:[(0,r.jsx)("h3",{className:"text-sm font-medium text-gray-900 dark:text-white mb-2",children:"What you'll get with a subscription:"}),(0,r.jsxs)("ul",{className:"text-sm text-gray-600 dark:text-gray-300 space-y-1",children:[(0,r.jsx)("li",{children:"• Unlimited BVA creation"}),(0,r.jsx)("li",{children:"• Access to all templates"}),(0,r.jsx)("li",{children:"• Advanced analytics"}),(0,r.jsx)("li",{children:"• Priority support"}),(0,r.jsx)("li",{children:"• Export to multiple formats"})]})]}),(0,r.jsxs)("div",{className:"space-y-3",children:[s&&(0,r.jsx)(l(),{href:"/pricing",className:"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block",children:"Upgrade Now"}),(0,r.jsx)(l(),{href:"/dashboard",className:"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block",children:"Back to Dashboard"})]})]})}):(0,r.jsx)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center",children:[(0,r.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-6xl mb-4",children:"\uD83D\uDD12"}),(0,r.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white mb-4",children:"Authentication Required"}),(0,r.jsxs)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:["Please log in to access ",a,"."]}),(0,r.jsxs)("div",{className:"space-y-3",children:[(0,r.jsx)(l(),{href:"/login",className:"w-full bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 inline-block",children:"Log In"}),(0,r.jsx)(l(),{href:"/register",className:"w-full bg-gray-200 dark:bg-gray-600 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-500 inline-block",children:"Start Free Trial"})]})]})})}function n(){let{user:e,canAccessPremiumFeatures:t}=(0,d.A)();return{hasAccess:t,isTrialUser:(null==e?void 0:e.isTrialUser)||!1,isTrialExpired:(null==e?void 0:e.trialExpired)||!1,user:e}}}},e=>{var t=t=>e(e.s=t);e.O(0,[992,965,288,874,63,573,441,684,358],()=>t(5383)),_N_E=e.O()}]);