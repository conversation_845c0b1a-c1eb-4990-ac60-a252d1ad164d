exports.id=127,exports.ids=[127],exports.modules={3465:(e,t,r)=>{"use strict";r.d(t,{ThemeProvider:()=>s});var i=r(12907);(0,i.registerClientReference)(function(){throw Error("Attempted to call useTheme() from the server but useTheme is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useTheme"),(0,i.registerClientReference)(function(){throw Error("Attempted to call useThemeSafe() from the server but useThemeSafe is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","useThemeSafe");let s=(0,i.registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\ThemeContext.tsx","ThemeProvider")},22049:(e,t,r)=>{Promise.resolve().then(r.bind(r,94442)),Promise.resolve().then(r.bind(r,3465))},30474:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var i=r(31261),s=r.n(i)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return o}});let i=r(37366),s=r(44953),a=r(46533),n=i._(r(1933));function o(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:n.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=a.Image},40303:(e,t,r)=>{"use strict";r.d(t,{Q:()=>n,ThemeProvider:()=>o});var i=r(60687),s=r(43210);let a=(0,s.createContext)(null),n=()=>(0,s.useContext)(a),o=({children:e})=>{let[t,r]=(0,s.useState)("light"),[n,o]=(0,s.useState)(!1);(0,s.useEffect)(()=>{o(!0);let e=localStorage.getItem("theme");if(e)r(e),l(e);else{let e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light";r(e),l(e)}},[]);let l=e=>{let t=document.documentElement;"dark"===e?t.classList.add("dark"):t.classList.remove("dark")};return n?(0,i.jsx)(a.Provider,{value:{theme:t,toggleTheme:()=>{let e="light"===t?"dark":"light";r(e),l(e),localStorage.setItem("theme",e)}},children:e}):(0,i.jsx)(i.Fragment,{children:e})}},46055:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var i=r(31658);let s=async e=>[{type:"image/x-icon",sizes:"16x16",url:(0,i.fillMetadataSegment)(".",await e.params,"favicon.ico")+""}]},51108:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,AuthProvider:()=>u});var i=r(60687),s=r(43210),a=r(19978),n=r(56304),o=r(75535),l=r(53836);let d=(0,s.createContext)(null),u=({children:e})=>{let[t,r]=(0,s.useState)(null),[u,c]=(0,s.useState)(null),[m,h]=(0,s.useState)(!0),[p,v]=(0,s.useState)(!1),[f,b]=(0,s.useState)(!1),[g,x]=(0,s.useState)(!1),w=async()=>{if(u)try{let e=await (0,o.x7)((0,o.H9)(n.db,"users",u.uid));if(e.exists()){let t={id:e.id,...e.data()},i=(0,l.gZ)(t);i&&!t.trialExpired&&(await (0,o.mZ)((0,o.H9)(n.db,"users",u.uid),{trialExpired:!0,updatedAt:new Date}),t.trialExpired=!0),r(t),v("admin"===t.role),b((0,l.nE)(t)),x(i)}}catch(e){console.error("Error refreshing user data:",e)}};(0,s.useEffect)(()=>{let e=(0,a.hg)(n.j2,async e=>{c(e),e?await w():(r(null),v(!1),b(!1),x(!1)),h(!1)});return()=>e()},[]),(0,s.useEffect)(()=>{u&&w()},[u]);let P=async(e,t)=>{await (0,a.x9)(n.j2,e,t)},y=async(e,t)=>{let r=await (0,a.eJ)(n.j2,e,t),i=(0,l.ow)(r.user.email||e);await (0,o.BN)((0,o.H9)(n.db,"users",r.user.uid),i)},C=async()=>{await (0,a.CI)(n.j2)},A=async()=>{let e=new a.HF,t=await (0,a.df)(n.j2,e);if(!(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()){let e=(0,l.ow)(t.user.email||"",{firstName:t.user.displayName?.split(" ")[0],lastName:t.user.displayName?.split(" ").slice(1).join(" ")});await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),e)}},E=async()=>{let e=new a.sk,t=await (0,a.df)(n.j2,e);if(!(await (0,o.x7)((0,o.H9)(n.db,"users",t.user.uid))).exists()){let e=(0,l.ow)(t.user.email||"",{firstName:t.user.displayName?.split(" ")[0],lastName:t.user.displayName?.split(" ").slice(1).join(" ")});await (0,o.BN)((0,o.H9)(n.db,"users",t.user.uid),e)}};return(0,i.jsx)(d.Provider,{value:{user:t,firebaseUser:u,loading:m,signIn:P,signUp:y,logOut:C,signInWithGoogle:A,signInWithFacebook:E,isAdmin:p,canAccessPremiumFeatures:f,isTrialExpired:g,refreshUserData:w},children:e})},c=()=>{let e=(0,s.useContext)(d);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},51861:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,86346,23)),Promise.resolve().then(r.t.bind(r,27924,23)),Promise.resolve().then(r.t.bind(r,35656,23)),Promise.resolve().then(r.t.bind(r,40099,23)),Promise.resolve().then(r.t.bind(r,38243,23)),Promise.resolve().then(r.t.bind(r,28827,23)),Promise.resolve().then(r.t.bind(r,62763,23)),Promise.resolve().then(r.t.bind(r,97173,23))},53836:(e,t,r)=>{"use strict";function i(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function s(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||i(e)))}function a(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let t=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let t=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-t.getTime())/864e5))}(e);return t<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:t<=3?{message:`Your trial expires in ${t} day${1===t?"":"s"}. Upgrade now to continue.`,type:"warning",daysRemaining:t}:{message:`Your trial expires in ${t} days on ${!e.trialEndDate?"":new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"})}.`,type:"info",daysRemaining:t}}function n(e,t={}){return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,t=new Date(e);return t.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:t,trialExpired:!1}}(),...t}}r.d(t,{Mo:()=>a,gZ:()=>i,nE:()=>s,ow:()=>n})},56304:(e,t,r)=>{"use strict";r.d(t,{db:()=>d,j2:()=>l});var i=r(67989),s=r(19978),a=r(75535),n=r(70146);let o=(0,i.Dk)().length?(0,i.Sx)():(0,i.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"***********",appId:"1:***********:web:c21e9ccb3abae564f29162"}),l=(0,s.xI)(o),d=(0,a.aU)(o);(0,n.c7)(o)},58014:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>l});var i=r(37413),s=r(61421),a=r.n(s);r(82704);var n=r(94442),o=r(3465);let l={title:"VALTICS AI System",description:"Business Value Analysis Platform"};function d({children:e}){return(0,i.jsx)("html",{lang:"en",children:(0,i.jsx)("body",{className:a().className,children:(0,i.jsx)(o.ThemeProvider,{children:(0,i.jsx)(n.AuthProvider,{children:e})})})})}},58497:(e,t,r)=>{Promise.resolve().then(r.bind(r,51108)),Promise.resolve().then(r.bind(r,40303))},61589:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,16444,23)),Promise.resolve().then(r.t.bind(r,16042,23)),Promise.resolve().then(r.t.bind(r,88170,23)),Promise.resolve().then(r.t.bind(r,49477,23)),Promise.resolve().then(r.t.bind(r,29345,23)),Promise.resolve().then(r.t.bind(r,12089,23)),Promise.resolve().then(r.t.bind(r,46577,23)),Promise.resolve().then(r.t.bind(r,31307,23))},82704:()=>{},94442:(e,t,r)=>{"use strict";r.d(t,{AuthProvider:()=>s});var i=r(12907);let s=(0,i.registerClientReference)(function(){throw Error("Attempted to call AuthProvider() from the server but AuthProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","AuthProvider");(0,i.registerClientReference)(function(){throw Error("Attempted to call useAuth() from the server but useAuth is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\contexts\\AuthContext.tsx","useAuth")}};