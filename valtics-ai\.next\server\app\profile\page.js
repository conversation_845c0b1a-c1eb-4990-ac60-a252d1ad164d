(()=>{var e={};e.id=636,e.ids=[636],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5481:(e,r,t)=>{"use strict";t.d(r,{A:()=>c});var a=t(60687),s=t(85814),d=t.n(s),l=t(30474),i=t(51108),n=t(16189),o=t(27436),x=t(31769);function c({title:e="VALTICS AI",showBackButton:r=!1,backUrl:t="/dashboard",backText:s="← Back to Dashboard"}){let{user:c,logOut:m,isAdmin:u}=(0,i.A)(),g=(0,n.useRouter)(),h=async()=>{try{await m(),g.push("/")}catch(e){console.error("Error logging out:",e)}};return c?(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,a.jsx)(l.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[r&&(0,a.jsx)(d(),{href:t,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:s}),!r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,a.jsx)(d(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),u&&(0,a.jsx)(d(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,a.jsx)(d(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,a.jsx)(x.I,{}),(0,a.jsx)(o.default,{}),(0,a.jsx)("button",{onClick:h,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},15667:(e,r,t)=>{Promise.resolve().then(t.bind(t,63393))},16189:(e,r,t)=>{"use strict";var a=t(65773);t.o(a,"useParams")&&t.d(r,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(r,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(r,{useSearchParams:function(){return a.useSearchParams}})},17199:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\profile\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\profile\\page.tsx","default")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27436:(e,r,t)=>{"use strict";t.d(r,{default:()=>l});var a=t(60687),s=t(40303),d=t(43210);function l(){let[e,r]=(0,d.useState)(!1),[t,l]=(0,d.useState)("light"),i=(0,s.Q)(),n=e=>{let r=document.documentElement;"dark"===e?r.classList.add("dark"):r.classList.remove("dark")};if(!e)return(0,a.jsx)("div",{className:"p-2 w-9 h-9"});let o=i?i.theme:t;return(0,a.jsx)("button",{onClick:()=>{if(i)i.toggleTheme();else{let e="light"===t?"dark":"light";l(e),n(e),localStorage.setItem("theme",e)}},className:"p-2 rounded-md text-gray-700 hover:text-gray-900 hover:bg-gray-100 dark:text-gray-300 dark:hover:text-gray-100 dark:hover:bg-gray-700 transition-colors","aria-label":`Switch to ${"light"===o?"dark":"light"} mode`,title:`Switch to ${"light"===o?"dark":"light"} mode`,children:"light"===o?(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"})}):(0,a.jsx)("svg",{className:"w-5 h-5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})})}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31769:(e,r,t)=>{"use strict";t.d(r,{A:()=>o,I:()=>x});var a=t(60687),s=t(43210),d=t(85814),l=t.n(d),i=t(51108),n=t(53836);function o(){let{user:e}=(0,i.A)(),[r,t]=(0,s.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||r)return null;let{message:d,type:o,daysRemaining:x}=(0,n.Mo)(e);if(!d)return null;let c=()=>{switch(o){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,a.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(o){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"error"===o?(0,a.jsx)("svg",{className:`h-5 w-5 ${c()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===o?(0,a.jsx)("svg",{className:`h-5 w-5 ${c()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:`h-5 w-5 ${c()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:d})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(l(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(o){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,a.jsx)("button",{onClick:()=>t(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function x(){let{user:e}=(0,i.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:r}=(0,n.Mo)(e);return r<=0?(0,a.jsx)(l(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,a.jsxs)(l(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${r<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[r," day",1===r?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},59e3:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>l.a,__next_app__:()=>c,pages:()=>x,routeModule:()=>m,tree:()=>o});var a=t(65239),s=t(48088),d=t(88170),l=t.n(d),i=t(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);t.d(r,n);let o={children:["",{children:["profile",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,17199)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\profile\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\profile\\page.tsx"],c={require:t,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/profile/page",pathname:"/profile",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},63393:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>x});var a=t(60687),s=t(43210),d=t(51108),l=t(16189),i=t(85814),n=t.n(i);t(75535),t(56304);var o=t(5481);function x(){let{user:e,firebaseUser:r,loading:t,logOut:i}=(0,d.A)(),x=(0,l.useRouter)(),[c,m]=(0,s.useState)(null),[u,g]=(0,s.useState)(null),[h,b]=(0,s.useState)([]),[p,y]=(0,s.useState)(!0),[v,k]=(0,s.useState)("profile"),[f,j]=(0,s.useState)(!1),w=async()=>{try{await i(),x.push("/")}catch(e){console.error("Error logging out:",e)}};if(t||p)return(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})});if(!e||!c)return null;let N=e=>{switch(e){case"completed":return"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200";case"in-progress":return"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200";default:return"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}};return(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(o.A,{title:"VALTICS AI",showBackButton:!0}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Profile & Settings"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Manage your account settings and view your activity."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"profile",name:"Profile"},{id:"subscription",name:"Subscription"},{id:"bvas",name:"My BVAs"}].map(e=>(0,a.jsx)("button",{onClick:()=>k(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${v===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:e.name},e.id))})}),"profile"===v&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Information"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Email Address"}),(0,a.jsx)("input",{type:"email",value:r?.email||"",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"}),(0,a.jsx)("p",{className:"text-xs text-gray-500 dark:text-gray-400 mt-1",children:"Email cannot be changed"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Account Type"}),(0,a.jsx)("input",{type:"text",value:"admin"===c.role?"Administrator":"Standard User",disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Member Since"}),(0,a.jsx)("input",{type:"text",value:new Date(c.createdAt).toLocaleDateString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Total BVAs Created"}),(0,a.jsx)("input",{type:"text",value:h.length.toString(),disabled:!0,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-700 text-gray-500 dark:text-gray-400"})]})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Account Actions"}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Change Password"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Update your account password"})]}),(0,a.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Change Password"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-gray-200 dark:border-gray-600 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:"Download Data"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:"Export all your BVA data"})]}),(0,a.jsx)("button",{className:"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-green-700 dark:hover:bg-green-800",children:"Export Data"})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between p-4 border border-red-200 dark:border-red-600 rounded-lg",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-red-900 dark:text-red-400",children:"Sign Out"}),(0,a.jsx)("p",{className:"text-sm text-red-600 dark:text-red-400",children:"Sign out of your account"})]}),(0,a.jsx)("button",{onClick:w,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Sign Out"})]})]})]})})]}),"subscription"===v&&(0,a.jsx)("div",{className:"space-y-6",children:u?(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Current Subscription"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Plan Type"}),(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"enterprise"===u.type?"bg-purple-100 dark:bg-purple-900 text-purple-800 dark:text-purple-200":"premium"===u.type?"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}`,children:u.type.charAt(0).toUpperCase()+u.type.slice(1)})})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Status"}),(0,a.jsx)("span",{className:`px-3 py-1 rounded-full text-sm font-medium ${"active"===u.status?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"cancelled"===u.status?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-gray-100 dark:bg-gray-700 text-gray-800 dark:text-gray-200"}`,children:u.status.charAt(0).toUpperCase()+u.status.slice(1)})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Start Date"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-white",children:new Date(u.startDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"End Date"}),(0,a.jsx)("p",{className:"text-sm text-gray-900 dark:text-white",children:new Date(u.endDate).toLocaleDateString()})]}),(0,a.jsxs)("div",{className:"md:col-span-2",children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Access"}),(0,a.jsxs)("p",{className:"text-sm text-gray-900 dark:text-white",children:[u.accessibleTemplateIds.length," templates available"]})]})]}),(0,a.jsxs)("div",{className:"mt-6 flex space-x-4",children:[(0,a.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Upgrade Plan"}),(0,a.jsx)("button",{className:"bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md text-sm font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Manage Billing"})]})]})}):(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6 text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-6xl mb-4",children:"\uD83D\uDCB3"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No Active Subscription"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300 mb-6",children:"Subscribe to access premium templates and features."}),(0,a.jsx)("button",{className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-3 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"View Subscription Plans"})]})})}),"bvas"===v&&(0,a.jsx)("div",{className:"space-y-6",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Your BVAs"}),h.length>0?(0,a.jsx)("div",{className:"space-y-4",children:h.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 dark:border-gray-600 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 dark:text-white",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Client: ",e.clientName]}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 dark:text-gray-500",children:["Last updated: ",new Date(e.updatedAt).toLocaleDateString()]})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${N(e.status)}`,children:e.status}),(0,a.jsx)(n(),{href:`/bva/${e.id}`,className:"text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium",children:"View"})]})]})},e.id))}):(0,a.jsxs)("div",{className:"text-center py-8",children:[(0,a.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-4xl mb-4",children:"\uD83D\uDCCA"}),(0,a.jsx)("p",{className:"text-gray-500 dark:text-gray-400",children:"No BVAs created yet."}),(0,a.jsx)(n(),{href:"/bva/new",className:"mt-4 inline-block bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Create Your First BVA"})]})]})})})]})})]})}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},78875:(e,r,t)=>{Promise.resolve().then(t.bind(t,17199))},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,823,567,533,127],()=>t(59e3));module.exports=a})();