(()=>{var e={};e.id=9,e.ids=[9],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5481:(e,t,r)=>{"use strict";r.d(t,{A:()=>u});var a=r(60687),s=r(85814),i=r.n(s),l=r(30474),d=r(51108),o=r(16189),n=r(27436),c=r(31769);function u({title:e="VALTICS AI",showBackButton:t=!1,backUrl:r="/dashboard",backText:s="← Back to Dashboard"}){let{user:u,logOut:m,isAdmin:x}=(0,d.A)(),p=(0,o.useRouter)(),g=async()=>{try{await m(),p.push("/")}catch(e){console.error("Error logging out:",e)}};return u?(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(i(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,a.jsx)(l.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&(0,a.jsx)(i(),{href:r,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:s}),!t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,a.jsx)(i(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),x&&(0,a.jsx)(i(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,a.jsx)(i(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,a.jsx)(c.I,{}),(0,a.jsx)(n.default,{}),(0,a.jsx)("button",{onClick:g,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},26180:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(60687),s=r(43210),i=r(51108),l=r(16189),d=r(85814),o=r.n(d),n=r(75535),c=r(56304),u=r(5481),m=r(49615);function x(){let{user:e,loading:t,isAdmin:r}=(0,i.A)(),d=(0,l.useRouter)(),x=(0,l.useParams)().id,[p,g]=(0,s.useState)(null),[b,h]=(0,s.useState)(null),[y,f]=(0,s.useState)(!0),[v,k]=(0,s.useState)(!1),[j,N]=(0,s.useState)("step2"),[w,D]=(0,s.useState)(!1),[C,P]=(0,s.useState)({enterpriseNeed:"",solutionDescriptionDocument:"",riskOfNoInvestment:""}),[q,A]=(0,s.useState)({solutionProviderName:"",solutionName:"",solutionDescription:"",templateVersion:"",templateVersionDate:"",category:"",price:0}),S=async()=>{try{f(!0);let e=await (0,n.x7)((0,n.H9)(c.db,"templates",x));if(e.exists()){let t={id:e.id,...e.data()};g(t),P({enterpriseNeed:t.enterpriseNeed||"",solutionDescriptionDocument:t.solutionDescriptionDocument||"",riskOfNoInvestment:t.riskOfNoInvestment||""}),A({solutionProviderName:t.solutionProviderName||"",solutionName:t.solutionName||"",solutionDescription:t.solutionDescription||"",templateVersion:t.templateVersion||"",templateVersionDate:t.templateVersionDate?t.templateVersionDate instanceof Date?t.templateVersionDate.toISOString().split("T")[0]:new Date(1e3*t.templateVersionDate.seconds).toISOString().split("T")[0]:"",category:t.category||"",price:t.price||0});let r=await (0,n.x7)((0,n.H9)(c.db,"brands",t.brandId));r.exists()&&h({id:r.id,...r.data()})}else d.push("/admin")}catch(e){console.error("Error fetching template data:",e),d.push("/admin")}finally{f(!1)}},I=async e=>{if(e.preventDefault(),p)try{k(!0),await (0,n.mZ)((0,n.H9)(c.db,"templates",x),{solutionProviderName:q.solutionProviderName,solutionName:q.solutionName,solutionDescription:q.solutionDescription,templateVersion:q.templateVersion,templateVersionDate:n.Dc.fromDate(new Date(q.templateVersionDate)),category:q.category,price:q.price,name:q.solutionName,description:q.solutionDescription,updatedAt:n.Dc.now()}),alert("Step 1 information updated successfully!"),await S()}catch(e){console.error("Error updating template:",e),alert("Error updating template. Please try again.")}finally{k(!1)}},V=async e=>{if(e.preventDefault(),p)try{k(!0),await (0,n.mZ)((0,n.H9)(c.db,"templates",x),{enterpriseNeed:C.enterpriseNeed,solutionDescriptionDocument:C.solutionDescriptionDocument,riskOfNoInvestment:C.riskOfNoInvestment,step2Completed:!0,updatedAt:n.Dc.now()}),alert("Documents saved successfully! You can now publish the template."),await S()}catch(e){console.error("Error updating template:",e),alert("Error updating template. Please try again.")}finally{k(!1)}},E=async()=>{if(p){if(!p.step1Completed||!p.step2Completed)return void alert("Please complete both steps before publishing.");if(!C.enterpriseNeed||!C.solutionDescriptionDocument||!C.riskOfNoInvestment)return void alert("Please fill in all three documents before publishing.");try{k(!0),await (0,n.mZ)((0,n.H9)(c.db,"templates",x),{status:"published",isActive:!0,updatedAt:n.Dc.now()}),alert("Template published successfully!"),d.push("/admin")}catch(e){console.error("Error publishing template:",e),alert("Error publishing template. Please try again.")}finally{k(!1)}}},T=async()=>{if(p)try{k(!0),await (0,n.mZ)((0,n.H9)(c.db,"templates",x),{enterpriseNeed:C.enterpriseNeed,solutionDescriptionDocument:C.solutionDescriptionDocument,riskOfNoInvestment:C.riskOfNoInvestment,status:"draft",updatedAt:n.Dc.now()}),alert("Template saved as draft successfully!")}catch(e){console.error("Error saving template:",e),alert("Error saving template. Please try again.")}finally{k(!1)}};return t||y?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):r&&p?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(u.A,{}),(0,a.jsx)("div",{className:"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Edit Template"}),(0,a.jsxs)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:[p.solutionName," - ",b?.name]}),(0,a.jsxs)("div",{className:"mt-2 flex items-center space-x-4",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"published"===p.status?"bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200":"bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200"}`,children:"published"===p.status?"Published":"Draft"}),(0,a.jsxs)("span",{className:"text-sm text-gray-500 dark:text-gray-400",children:["Version ",p.templateVersion]})]})]}),(0,a.jsx)(o(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Back to Admin"})]})}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:`flex items-center ${p.step1Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"}`,children:[(0,a.jsx)("div",{className:`flex items-center justify-center w-8 h-8 ${p.step1Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"} text-white rounded-full text-sm font-medium`,children:p.step1Completed?"✓":"1"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Basic Information"})]}),(0,a.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:`flex items-center ${p.step2Completed?"text-green-600 dark:text-green-400":"text-blue-600 dark:text-blue-400"}`,children:[(0,a.jsx)("div",{className:`flex items-center justify-center w-8 h-8 ${p.step2Completed?"bg-green-600 dark:bg-green-500":"bg-blue-600 dark:bg-blue-500"} text-white rounded-full text-sm font-medium`,children:p.step2Completed?"✓":"2"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Documents"})]})]})}),(0,a.jsx)("div",{className:"mb-6",children:(0,a.jsxs)("div",{className:"flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg",children:[(0,a.jsx)("button",{onClick:()=>N("step1"),className:`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${"step1"===j?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"}`,children:"Edit Basic Information"}),(0,a.jsx)("button",{onClick:()=>N("step2"),className:`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${"step2"===j?"bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow":"text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200"}`,children:"Edit Documents"})]})}),"step1"===j&&(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:I,className:"p-6 space-y-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Basic Information"}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Provider Name *"}),(0,a.jsx)("input",{type:"text",value:q.solutionProviderName,onChange:e=>A(t=>({...t,solutionProviderName:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Name *"}),(0,a.jsx)("input",{type:"text",value:q.solutionName,onChange:e=>A(t=>({...t,solutionName:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Description *"}),(0,a.jsx)(m.A,{value:q.solutionDescription,onChange:e=>A(t=>({...t,solutionDescription:e})),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version *"}),(0,a.jsx)("input",{type:"text",value:q.templateVersion,onChange:e=>A(t=>({...t,templateVersion:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version Date *"}),(0,a.jsx)("input",{type:"date",value:q.templateVersionDate,onChange:e=>A(t=>({...t,templateVersionDate:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:q.category,onChange:e=>A(t=>({...t,category:e.target.value})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"Business Analysis",children:"Business Analysis"}),(0,a.jsx)("option",{value:"Financial Planning",children:"Financial Planning"}),(0,a.jsx)("option",{value:"Technology Assessment",children:"Technology Assessment"}),(0,a.jsx)("option",{value:"Risk Management",children:"Risk Management"}),(0,a.jsx)("option",{value:"Strategic Planning",children:"Strategic Planning"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Price ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:q.price,onChange:e=>A(t=>({...t,price:parseFloat(e.target.value)||0})),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]})]}),(0,a.jsx)("div",{className:"flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700",children:(0,a.jsx)("button",{type:"submit",disabled:v,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Updating...":"Update Basic Information"})})]})}),"step2"===j&&(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:V,className:"p-6 space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"Generate Documents"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 dark:text-gray-400 mb-6",children:"Create the three essential documents for your Business Value Analysis template."})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Enterprise Need *"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Describe the business need or challenge that this solution addresses."}),(0,a.jsx)(m.A,{value:C.enterpriseNeed,onChange:e=>P(t=>({...t,enterpriseNeed:e})),placeholder:"Describe the enterprise need that this solution addresses...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Description *"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Provide a comprehensive description of how your solution works and its key features."}),(0,a.jsx)(m.A,{value:C.solutionDescriptionDocument,onChange:e=>P(t=>({...t,solutionDescriptionDocument:e})),placeholder:"Provide a detailed description of your solution...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Risk of No Investment *"}),(0,a.jsx)("p",{className:"text-sm text-gray-500 dark:text-gray-400 mb-3",children:"Explain the potential risks and consequences of not implementing this solution."}),(0,a.jsx)(m.A,{value:C.riskOfNoInvestment,onChange:e=>P(t=>({...t,riskOfNoInvestment:e})),placeholder:"Describe the risks of not investing in this solution...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{className:"flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)("button",{type:"button",onClick:T,disabled:v,className:"bg-gray-600 dark:bg-gray-700 text-white px-6 py-2 rounded-md font-medium hover:bg-gray-700 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Saving...":"Save as Draft"}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"submit",disabled:v,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Saving...":"Save Documents"}),p.step2Completed&&(0,a.jsx)("button",{type:"button",onClick:E,disabled:v||"published"===p.status,className:"bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed",children:"published"===p.status?"Published":v?"Publishing...":"Publish Template"})]})]})]})})]})})]}):null}},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31769:(e,t,r)=>{"use strict";r.d(t,{A:()=>n,I:()=>c});var a=r(60687),s=r(43210),i=r(85814),l=r.n(i),d=r(51108),o=r(53836);function n(){let{user:e}=(0,d.A)(),[t,r]=(0,s.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||t)return null;let{message:i,type:n,daysRemaining:c}=(0,o.Mo)(e);if(!i)return null;let u=()=>{switch(n){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,a.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(n){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"error"===n?(0,a.jsx)("svg",{className:`h-5 w-5 ${u()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===n?(0,a.jsx)("svg",{className:`h-5 w-5 ${u()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:`h-5 w-5 ${u()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:i})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(l(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(n){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,a.jsx)("button",{onClick:()=>r(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function c(){let{user:e}=(0,d.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:t}=(0,o.Mo)(e);return t<=0?(0,a.jsx)(l(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,a.jsxs)(l(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${t<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[t," day",1===t?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},41765:(e,t,r)=>{Promise.resolve().then(r.bind(r,64118))},49615:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687),s=r(76180),i=r.n(s),l=r(30036);r(23624);let d=(0,l.default)(async()=>{},{loadableGenerated:{modules:["components\\RichTextEditor.tsx -> react-quill"]},ssr:!1}),o=({value:e,onChange:t,placeholder:r="Enter text...",className:s="",disabled:l=!1})=>{let o={toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],["link"],[{align:[]}],["clean"]]};return(0,a.jsxs)("div",{className:`jsx-3979297ce4da0409 rich-text-editor ${s}`,children:[(0,a.jsx)(d,{theme:"snow",value:e,onChange:t,modules:o,formats:["header","bold","italic","underline","strike","list","bullet","indent","link","align"],placeholder:r,readOnly:l,style:{backgroundColor:l?"#f9fafb":"white"}}),(0,a.jsx)(i(),{id:"3979297ce4da0409",children:".rich-text-editor .ql-editor{min-height:150px}.rich-text-editor .ql-toolbar{border-top:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.rich-text-editor .ql-container{border-bottom:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.dark .rich-text-editor .ql-toolbar{border-color:#4b5563;background-color:#374151}.dark .rich-text-editor .ql-container{border-color:#4b5563;background-color:#1f2937}.dark .rich-text-editor .ql-editor{color:#f9fafb}.dark .rich-text-editor .ql-toolbar .ql-stroke{stroke:#9ca3af}.dark .rich-text-editor .ql-toolbar .ql-fill{fill:#9ca3af}"})]})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64118:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\templates\\\\[id]\\\\edit\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\[id]\\edit\\page.tsx","default")},71269:(e,t,r)=>{Promise.resolve().then(r.bind(r,26180))},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},77492:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>n});var a=r(65239),s=r(48088),i=r(88170),l=r.n(i),d=r(30893),o={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>d[e]);r.d(t,o);let n={children:["",{children:["admin",{children:["templates",{children:["[id]",{children:["edit",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,64118)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\[id]\\edit\\page.tsx"]}]},{}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\[id]\\edit\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/templates/[id]/edit/page",pathname:"/admin/templates/[id]/edit",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")}};var t=require("../../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,823,567,533,349,77],()=>r(77492));module.exports=a})();