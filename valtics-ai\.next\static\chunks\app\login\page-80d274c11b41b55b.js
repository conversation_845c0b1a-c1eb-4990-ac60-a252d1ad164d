(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[520],{1138:(e,r,a)=>{"use strict";a.d(r,{db:()=>u,j2:()=>l});var t=a(3915),s=a(6203),i=a(5317),d=a(858);let n=(0,t.Dk)().length?(0,t.Sx)():(0,t.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),l=(0,s.xI)(n),u=(0,i.aU)(n);(0,d.c7)(n)},1469:(e,r,a)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),!function(e,r){for(var a in r)Object.defineProperty(e,a,{enumerable:!0,get:r[a]})}(r,{default:function(){return l},getImageProps:function(){return n}});let t=a(8229),s=a(8883),i=a(3063),d=t._(a(1193));function n(e){let{props:r}=(0,s.getImgProps)(e,{defaultLoader:d.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,a]of Object.entries(r))void 0===a&&delete r[e];return{props:r}}let l=i.Image},3274:(e,r,a)=>{"use strict";a.d(r,{A:()=>c,AuthProvider:()=>o});var t=a(5155),s=a(2115),i=a(6203),d=a(1138),n=a(5317),l=a(9886);let u=(0,s.createContext)(null),o=e=>{let{children:r}=e,[a,o]=(0,s.useState)(null),[c,m]=(0,s.useState)(null),[g,x]=(0,s.useState)(!0),[b,h]=(0,s.useState)(!1),[f,y]=(0,s.useState)(!1),[p,w]=(0,s.useState)(!1),v=async()=>{if(c)try{let e=await (0,n.x7)((0,n.H9)(d.db,"users",c.uid));if(e.exists()){let r={id:e.id,...e.data()},a=(0,l.gZ)(r);a&&!r.trialExpired&&(await (0,n.mZ)((0,n.H9)(d.db,"users",c.uid),{trialExpired:!0,updatedAt:new Date}),r.trialExpired=!0),o(r),h("admin"===r.role),y((0,l.nE)(r)),w(a)}}catch(e){console.error("Error refreshing user data:",e)}};(0,s.useEffect)(()=>{let e=(0,i.hg)(d.j2,async e=>{m(e),e?await v():(o(null),h(!1),y(!1),w(!1)),x(!1)});return()=>e()},[]),(0,s.useEffect)(()=>{c&&v()},[c]);let j=async(e,r)=>{await (0,i.x9)(d.j2,e,r)},k=async(e,r)=>{let a=await (0,i.eJ)(d.j2,e,r),t=(0,l.ow)(a.user.email||e);await (0,n.BN)((0,n.H9)(d.db,"users",a.user.uid),t)},N=async()=>{await (0,i.CI)(d.j2)},S=async()=>{let e=new i.HF,r=await (0,i.df)(d.j2,e);if(!(await (0,n.x7)((0,n.H9)(d.db,"users",r.user.uid))).exists()){var a,t;let e=(0,l.ow)(r.user.email||"",{firstName:null==(a=r.user.displayName)?void 0:a.split(" ")[0],lastName:null==(t=r.user.displayName)?void 0:t.split(" ").slice(1).join(" ")});await (0,n.BN)((0,n.H9)(d.db,"users",r.user.uid),e)}},D=async()=>{let e=new i.sk,r=await (0,i.df)(d.j2,e);if(!(await (0,n.x7)((0,n.H9)(d.db,"users",r.user.uid))).exists()){var a,t;let e=(0,l.ow)(r.user.email||"",{firstName:null==(a=r.user.displayName)?void 0:a.split(" ")[0],lastName:null==(t=r.user.displayName)?void 0:t.split(" ").slice(1).join(" ")});await (0,n.BN)((0,n.H9)(d.db,"users",r.user.uid),e)}};return(0,t.jsx)(u.Provider,{value:{user:a,firebaseUser:c,loading:g,signIn:j,signUp:k,logOut:N,signInWithGoogle:S,signInWithFacebook:D,isAdmin:b,canAccessPremiumFeatures:f,isTrialExpired:p,refreshUserData:v},children:r})},c=()=>{let e=(0,s.useContext)(u);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},5489:(e,r,a)=>{Promise.resolve().then(a.bind(a,8007))},5695:(e,r,a)=>{"use strict";var t=a(8999);a.o(t,"useParams")&&a.d(r,{useParams:function(){return t.useParams}}),a.o(t,"useRouter")&&a.d(r,{useRouter:function(){return t.useRouter}}),a.o(t,"useSearchParams")&&a.d(r,{useSearchParams:function(){return t.useSearchParams}})},6766:(e,r,a)=>{"use strict";a.d(r,{default:()=>s.a});var t=a(1469),s=a.n(t)},8007:(e,r,a)=>{"use strict";a.r(r),a.d(r,{default:()=>o});var t=a(5155),s=a(2115),i=a(3274),d=a(5695),n=a(6874),l=a.n(n),u=a(6766);function o(){let[e,r]=(0,s.useState)(""),[a,n]=(0,s.useState)(""),[o,c]=(0,s.useState)(""),{signIn:m,signInWithGoogle:g,signInWithFacebook:x}=(0,i.A)(),b=(0,d.useRouter)(),h=async r=>{r.preventDefault(),c("");try{await m(e,a),b.push("/dashboard")}catch(e){c(e.message)}},f=async()=>{try{await g(),b.push("/dashboard")}catch(e){c(e.message)}},y=async()=>{try{await x(),b.push("/dashboard")}catch(e){c(e.message)}};return(0,t.jsx)("div",{className:"flex min-h-screen flex-col items-center justify-center p-24 bg-gray-50 dark:bg-gray-900",children:(0,t.jsxs)("div",{className:"w-full max-w-md p-8 space-y-8 bg-white dark:bg-gray-800 rounded-lg shadow-md",children:[(0,t.jsxs)("div",{className:"text-center",children:[(0,t.jsx)("div",{className:"flex justify-center mb-4",children:(0,t.jsx)(u.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:64,height:64,className:"w-16 h-16"})}),(0,t.jsx)("h1",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"Login to VALTICS AI"})]}),o&&(0,t.jsx)("div",{className:"bg-red-100 dark:bg-red-900 border border-red-400 dark:border-red-600 text-red-700 dark:text-red-200 px-4 py-3 rounded",children:o}),(0,t.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"email",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Email"}),(0,t.jsx)("input",{id:"email",name:"email",type:"email",required:!0,value:e,onChange:e=>r(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsxs)("div",{children:[(0,t.jsx)("label",{htmlFor:"password",className:"block text-sm font-medium text-gray-700 dark:text-gray-300",children:"Password"}),(0,t.jsx)("input",{id:"password",name:"password",type:"password",required:!0,value:a,onChange:e=>n(e.target.value),className:"mt-1 block w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-blue-500 focus:border-blue-500"})]}),(0,t.jsx)("div",{children:(0,t.jsx)("button",{type:"submit",className:"w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500",children:"Sign in"})})]}),(0,t.jsxs)("div",{className:"mt-6",children:[(0,t.jsxs)("div",{className:"relative",children:[(0,t.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,t.jsx)("div",{className:"w-full border-t border-gray-300 dark:border-gray-600"})}),(0,t.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,t.jsx)("span",{className:"px-2 bg-white dark:bg-gray-800 text-gray-500 dark:text-gray-400",children:"Or continue with"})})]}),(0,t.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,t.jsx)("button",{onClick:f,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Google"}),(0,t.jsx)("button",{onClick:y,className:"w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm bg-white dark:bg-gray-700 text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-600",children:"Facebook"})]})]}),(0,t.jsx)("div",{className:"text-center mt-4",children:(0,t.jsxs)("p",{className:"text-sm text-gray-600 dark:text-gray-400",children:["Don't have an account?"," ",(0,t.jsx)(l(),{href:"/register",className:"font-medium text-blue-600 dark:text-blue-400 hover:text-blue-500 dark:hover:text-blue-300",children:"Register"})]})})]})})}},9886:(e,r,a)=>{"use strict";function t(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function s(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||t(e)))}function i(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let r=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let r=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-r.getTime())/864e5))}(e);return r<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:r<=3?{message:"Your trial expires in ".concat(r," day").concat(1===r?"":"s",". Upgrade now to continue."),type:"warning",daysRemaining:r}:{message:"Your trial expires in ".concat(r," days on ").concat(e.trialEndDate?new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"","."),type:"info",daysRemaining:r}}function d(e){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,r=new Date(e);return r.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:r,trialExpired:!1}}(),...r}}a.d(r,{Mo:()=>i,gZ:()=>t,nE:()=>s,ow:()=>d})}},e=>{var r=r=>e(e.s=r);e.O(0,[992,965,288,874,63,441,684,358],()=>r(5489)),_N_E=e.O()}]);