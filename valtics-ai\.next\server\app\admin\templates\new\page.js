(()=>{var e={};e.id=843,e.ids=[843],e.modules={1864:(e,r,t)=>{Promise.resolve().then(t.bind(t,97588))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5481:(e,r,t)=>{"use strict";t.d(r,{A:()=>u});var a=t(60687),s=t(85814),l=t.n(s),d=t(30474),i=t(51108),o=t(16189),n=t(27436),c=t(31769);function u({title:e="VALTICS AI",showBackButton:r=!1,backUrl:t="/dashboard",backText:s="← Back to Dashboard"}){let{user:u,logOut:x,isAdmin:m}=(0,i.A)(),g=(0,o.useRouter)(),b=async()=>{try{await x(),g.push("/")}catch(e){console.error("Error logging out:",e)}};return u?(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(l(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,a.jsx)(d.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[r&&(0,a.jsx)(l(),{href:t,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:s}),!r&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(l(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,a.jsx)(l(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),m&&(0,a.jsx)(l(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,a.jsx)(l(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,a.jsx)(c.I,{}),(0,a.jsx)(n.default,{}),(0,a.jsx)("button",{onClick:b,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14985:e=>{"use strict";e.exports=require("dns")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},31769:(e,r,t)=>{"use strict";t.d(r,{A:()=>n,I:()=>c});var a=t(60687),s=t(43210),l=t(85814),d=t.n(l),i=t(51108),o=t(53836);function n(){let{user:e}=(0,i.A)(),[r,t]=(0,s.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||r)return null;let{message:l,type:n,daysRemaining:c}=(0,o.Mo)(e);if(!l)return null;let u=()=>{switch(n){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,a.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(n){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"error"===n?(0,a.jsx)("svg",{className:`h-5 w-5 ${u()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===n?(0,a.jsx)("svg",{className:`h-5 w-5 ${u()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:`h-5 w-5 ${u()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:l})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(d(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(n){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,a.jsx)("button",{onClick:()=>t(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function c(){let{user:e}=(0,i.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:r}=(0,o.Mo)(e);return r<=0?(0,a.jsx)(d(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,a.jsxs)(d(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${r<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[r," day",1===r?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34328:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>d.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>n});var a=t(65239),s=t(48088),l=t(88170),d=t.n(l),i=t(30893),o={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>i[e]);t.d(r,o);let n={children:["",{children:["admin",{children:["templates",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,97588)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\new\\page.tsx"]}]},{}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,c=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},x=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/templates/new/page",pathname:"/admin/templates/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:n}})},34631:e=>{"use strict";e.exports=require("tls")},49615:(e,r,t)=>{"use strict";t.d(r,{A:()=>n});var a=t(60687),s=t(76180),l=t.n(s),d=t(43210),i=t(30036);t(23624);let o=(0,i.default)(async()=>{},{loadableGenerated:{modules:["components\\RichTextEditor.tsx -> react-quill"]},ssr:!1}),n=({value:e,onChange:r,placeholder:t="Enter text...",className:s="",disabled:i=!1})=>{let n=(0,d.useRef)(null),c={toolbar:[[{header:[1,2,3,4,5,6,!1]}],["bold","italic","underline","strike"],[{list:"ordered"},{list:"bullet"}],[{indent:"-1"},{indent:"+1"}],["link"],[{align:[]}],["clean"]]};return(0,a.jsxs)("div",{className:`jsx-3979297ce4da0409 rich-text-editor ${s}`,children:[(0,a.jsx)(o,{ref:n,theme:"snow",value:e,onChange:r,modules:c,formats:["header","bold","italic","underline","strike","list","bullet","indent","link","align"],placeholder:t,readOnly:i,style:{backgroundColor:i?"#f9fafb":"white"}}),(0,a.jsx)(l(),{id:"3979297ce4da0409",children:".rich-text-editor .ql-editor{min-height:150px}.rich-text-editor .ql-toolbar{border-top:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.rich-text-editor .ql-container{border-bottom:1px solid#e5e7eb;border-left:1px solid#e5e7eb;border-right:1px solid#e5e7eb}.dark .rich-text-editor .ql-toolbar{border-color:#4b5563;background-color:#374151}.dark .rich-text-editor .ql-container{border-color:#4b5563;background-color:#1f2937}.dark .rich-text-editor .ql-editor{color:#f9fafb}.dark .rich-text-editor .ql-toolbar .ql-stroke{stroke:#9ca3af}.dark .rich-text-editor .ql-toolbar .ql-fill{fill:#9ca3af}"})]})}},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66162:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>m});var a=t(60687),s=t(43210),l=t(51108),d=t(16189),i=t(85814),o=t.n(i),n=t(75535),c=t(56304),u=t(5481),x=t(49615);function m(){let{user:e,loading:r,isAdmin:t}=(0,l.A)(),i=(0,d.useRouter)(),[m,g]=(0,s.useState)([]),[b,h]=(0,s.useState)(!0),[p,y]=(0,s.useState)(!1),[v,f]=(0,s.useState)(!1),[k,j]=(0,s.useState)({brandId:"",solutionProviderName:"",solutionName:"",solutionDescription:"",templateVersion:"1.0",templateVersionDate:new Date().toISOString().split("T")[0],category:"Business Analysis",price:0}),[w,N]=(0,s.useState)({name:"",description:"",logoUrl:""}),C=async()=>{try{h(!0);let e=(0,n.P)((0,n.rJ)(c.db,"brands"),(0,n._M)("isActive","==",!0)),r=(await (0,n.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));g(r)}catch(e){console.error("Error fetching brands:",e)}finally{h(!1)}},q=(e,r)=>{if(j(t=>({...t,[e]:r})),"brandId"===e&&r){let e=m.find(e=>e.id===r);e&&j(r=>({...r,solutionProviderName:e.name}))}},P=async e=>{e.preventDefault();try{let e=await (0,n.gS)((0,n.rJ)(c.db,"brands"),{...w,isActive:!0,createdAt:n.Dc.now()});await C(),j(r=>({...r,brandId:e.id,solutionProviderName:w.name})),N({name:"",description:"",logoUrl:""}),y(!1),alert("Brand created successfully!")}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},A=async e=>{if(e.preventDefault(),!k.brandId||!k.solutionName||!k.solutionDescription)return void alert("Please fill in all required fields.");try{f(!0);let e={name:k.solutionName,description:k.solutionDescription,brandId:k.brandId,category:k.category,price:k.price,solutionProviderName:k.solutionProviderName,solutionName:k.solutionName,solutionDescription:k.solutionDescription,templateVersion:k.templateVersion,templateVersionDate:n.Dc.fromDate(new Date(k.templateVersionDate)),status:"draft",step1Completed:!0,step2Completed:!1,isActive:!1,fileUrls:{},createdAt:n.Dc.now(),updatedAt:n.Dc.now()},r=await (0,n.gS)((0,n.rJ)(c.db,"templates"),e);i.push(`/admin/templates/${r.id}/edit`)}catch(e){console.error("Error creating template:",e),alert("Error creating template. Please try again.")}finally{f(!1)}};return r||b?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):t?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(u.A,{}),(0,a.jsx)("div",{className:"max-w-4xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Create New Template"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Step 1: Basic Information"})]}),(0,a.jsx)(o(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Back to Admin"})]})}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsxs)("div",{className:"flex items-center text-blue-600 dark:text-blue-400",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-blue-600 dark:bg-blue-500 text-white rounded-full text-sm font-medium",children:"1"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Basic Information"})]}),(0,a.jsx)("div",{className:"flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"}),(0,a.jsxs)("div",{className:"flex items-center text-gray-400 dark:text-gray-500",children:[(0,a.jsx)("div",{className:"flex items-center justify-center w-8 h-8 bg-gray-200 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded-full text-sm font-medium",children:"2"}),(0,a.jsx)("span",{className:"ml-2 text-sm font-medium",children:"Documents"})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 shadow rounded-lg",children:(0,a.jsxs)("form",{onSubmit:A,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Company/Brand *"}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsxs)("select",{value:k.brandId,onChange:e=>q("brandId",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0,children:[(0,a.jsx)("option",{value:"",children:"Select a brand..."}),m.map(e=>(0,a.jsx)("option",{value:e.id,children:e.name},e.id))]}),(0,a.jsx)("button",{type:"button",onClick:()=>y(!0),className:"bg-green-600 dark:bg-green-700 text-white px-4 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 whitespace-nowrap",children:"Create Brand"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Provider Name *"}),(0,a.jsx)("input",{type:"text",value:k.solutionProviderName,onChange:e=>q("solutionProviderName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter solution provider name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Name *"}),(0,a.jsx)("input",{type:"text",value:k.solutionName,onChange:e=>q("solutionName",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter solution name",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Solution Description *"}),(0,a.jsx)(x.A,{value:k.solutionDescription,onChange:e=>q("solutionDescription",e),placeholder:"Enter detailed solution description...",className:"border border-gray-300 dark:border-gray-600 rounded-md"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version *"}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("input",{type:"text",value:k.templateVersion,onChange:e=>q("templateVersion",e.target.value),className:"flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"e.g., 1.0",required:!0}),(0,a.jsx)("button",{type:"button",onClick:()=>{let e=new Date,r=`${e.getFullYear()}.${(e.getMonth()+1).toString().padStart(2,"0")}.${e.getDate().toString().padStart(2,"0")}`;j(e=>({...e,templateVersion:r}))},className:"bg-blue-600 dark:bg-blue-700 text-white px-3 py-2 rounded-md text-sm font-medium hover:bg-blue-700 dark:hover:bg-blue-800 whitespace-nowrap",children:"Auto Generate"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Template Version Date *"}),(0,a.jsx)("input",{type:"date",value:k.templateVersionDate,onChange:e=>q("templateVersionDate",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Category"}),(0,a.jsxs)("select",{value:k.category,onChange:e=>q("category",e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"Business Analysis",children:"Business Analysis"}),(0,a.jsx)("option",{value:"Financial Planning",children:"Financial Planning"}),(0,a.jsx)("option",{value:"Technology Assessment",children:"Technology Assessment"}),(0,a.jsx)("option",{value:"Risk Management",children:"Risk Management"}),(0,a.jsx)("option",{value:"Strategic Planning",children:"Strategic Planning"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Price ($)"}),(0,a.jsx)("input",{type:"number",min:"0",step:"0.01",value:k.price,onChange:e=>q("price",parseFloat(e.target.value)||0),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"0.00"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-3 pt-6 border-t border-gray-200 dark:border-gray-700",children:[(0,a.jsx)(o(),{href:"/admin",className:"bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-6 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600",children:"Cancel"}),(0,a.jsx)("button",{type:"submit",disabled:v,className:"bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed",children:v?"Creating...":"Continue to Step 2"})]})]})})]})}),p&&(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-4",children:"Create New Brand"}),(0,a.jsxs)("form",{onSubmit:P,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Brand Name *"}),(0,a.jsx)("input",{type:"text",value:w.name,onChange:e=>N({...w,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:w.description,onChange:e=>N({...w,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:w.logoUrl,onChange:e=>N({...w,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Create Brand"}),(0,a.jsx)("button",{type:"button",onClick:()=>y(!1),className:"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Cancel"})]})]})]})})})]}):null}},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88712:(e,r,t)=>{Promise.resolve().then(t.bind(t,66162))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},97588:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});let a=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\templates\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\templates\\new\\page.tsx","default")}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[447,823,567,533,349,77],()=>t(34328));module.exports=a})();