(()=>{var e={};e.id=698,e.ids=[698],e.modules={533:(e,t,r)=>{Promise.resolve().then(r.bind(r,49441))},3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},5481:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(60687),s=r(85814),d=r.n(s),l=r(30474),i=r(51108),n=r(16189),c=r(27436),x=r(31769);function o({title:e="VALTICS AI",showBackButton:t=!1,backUrl:r="/dashboard",backText:s="← Back to Dashboard"}){let{user:o,logOut:m,isAdmin:u}=(0,i.A)(),h=(0,n.useRouter)(),g=async()=>{try{await m(),h.push("/")}catch(e){console.error("Error logging out:",e)}};return o?(0,a.jsx)("nav",{className:"bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsxs)(d(),{href:"/dashboard",className:"flex items-center space-x-3",children:[(0,a.jsx)(l.default,{src:"/logo.png",alt:"VALTICS AI Logo",width:32,height:32,className:"w-8 h-8"}),(0,a.jsx)("span",{className:"text-xl font-semibold text-gray-900 dark:text-white",children:e})]})}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[t&&(0,a.jsx)(d(),{href:r,className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:s}),!t&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(d(),{href:"/brands",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Brands"}),(0,a.jsx)(d(),{href:"/templates",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Templates"}),u&&(0,a.jsx)(d(),{href:"/admin",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Admin"}),(0,a.jsx)(d(),{href:"/profile",className:"text-gray-700 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white px-3 py-2 rounded-md text-sm font-medium",children:"Profile"})]}),(0,a.jsx)(x.I,{}),(0,a.jsx)(c.default,{}),(0,a.jsx)("button",{onClick:g,className:"bg-red-600 dark:bg-red-700 text-white px-4 py-2 rounded-md text-sm font-medium hover:bg-red-700 dark:hover:bg-red-800",children:"Logout"})]})]})})}):null}},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},14101:(e,t,r)=>{Promise.resolve().then(r.bind(r,20435))},14985:e=>{"use strict";e.exports=require("dns")},16189:(e,t,r)=>{"use strict";var a=r(65773);r.o(a,"useParams")&&r.d(t,{useParams:function(){return a.useParams}}),r.o(a,"useRouter")&&r.d(t,{useRouter:function(){return a.useRouter}}),r.o(a,"useSearchParams")&&r.d(t,{useSearchParams:function(){return a.useSearchParams}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19771:e=>{"use strict";e.exports=require("process")},20435:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var a=r(60687),s=r(43210),d=r(51108),l=r(16189),i=r(85814),n=r.n(i),c=r(75535),x=r(19978),o=r(56304),m=r(5481);function u(){let{user:e,loading:t,isAdmin:r}=(0,d.A)();(0,l.useRouter)();let[i,u]=(0,s.useState)([]),[g,p]=(0,s.useState)([]),[b,y]=(0,s.useState)([]),[v,f]=(0,s.useState)([]),[j,w]=(0,s.useState)(!0),[N,k]=(0,s.useState)("overview"),[A,C]=(0,s.useState)(!1),[P,S]=(0,s.useState)({name:"",description:"",logoUrl:""}),[D,L]=(0,s.useState)(null),[E,U]=(0,s.useState)(!1),[q,R]=(0,s.useState)(""),[$,M]=(0,s.useState)("all"),[I,_]=(0,s.useState)("all"),B=async()=>{try{w(!0);let e=(0,c.P)((0,c.rJ)(o.db,"brands"),(0,c.My)("name","asc")),t=(await (0,c.GG)(e)).docs.map(e=>({id:e.id,...e.data()}));u(t);let r=(0,c.P)((0,c.rJ)(o.db,"templates"),(0,c.My)("createdAt","desc")),a=(await (0,c.GG)(r)).docs.map(e=>({id:e.id,...e.data()}));p(a);let s=(0,c.P)((0,c.rJ)(o.db,"users"),(0,c.My)("createdAt","desc")),d=(await (0,c.GG)(s)).docs.map(e=>({id:e.id,...e.data()}));y(d);let l=(0,c.P)((0,c.rJ)(o.db,"bvaInstances"),(0,c.My)("createdAt","desc")),i=(await (0,c.GG)(l)).docs.map(e=>({id:e.id,...e.data()}));f(i)}catch(e){console.error("Error fetching admin data:",e)}finally{w(!1)}},z=async e=>{e.preventDefault();try{await (0,c.gS)((0,c.rJ)(o.db,"brands"),{...P,isActive:!0,createdAt:new Date}),S({name:"",description:"",logoUrl:""}),C(!1),B()}catch(e){console.error("Error creating brand:",e),alert("Error creating brand. Please try again.")}},T=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(o.db,"brands",e),{isActive:!t}),B()}catch(e){console.error("Error updating brand status:",e)}},G=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(o.db,"templates",e),{isActive:!t}),B()}catch(e){console.error("Error updating template status:",e)}},V=e=>{L(e),U(!0)},F=async e=>{if(D)try{await (0,c.mZ)((0,c.H9)(o.db,"users",D.id),{...e,updatedAt:new Date}),U(!1),L(null),B(),alert("User updated successfully!")}catch(e){console.error("Error updating user:",e),alert("Error updating user. Please try again.")}},H=async(e,t)=>{try{await (0,c.mZ)((0,c.H9)(o.db,"users",e),{isActive:!t,updatedAt:new Date}),B()}catch(e){console.error("Error updating user status:",e),alert("Error updating user status. Please try again.")}},J=async e=>{try{let t=(0,x.xI)();await (0,x.J1)(t,e),alert(`Password reset email sent to ${e}`)}catch(e){console.error("Error sending password reset email:",e),alert("Error sending password reset email. Please try again.")}},O=b.filter(e=>{let t=e.email.toLowerCase().includes(q.toLowerCase())||e.firstName&&e.firstName.toLowerCase().includes(q.toLowerCase())||e.lastName&&e.lastName.toLowerCase().includes(q.toLowerCase()),r="all"===$||e.role===$,a="all"===I||"active"===I&&e.isActive||"inactive"===I&&!e.isActive;return t&&r&&a});return t||j?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-900",children:(0,a.jsx)("div",{className:"text-xl text-gray-900 dark:text-white",children:"Loading..."})}):e&&r?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50 dark:bg-gray-900",children:[(0,a.jsx)(m.A,{title:"VALTICS AI - Admin"}),(0,a.jsx)("div",{className:"max-w-7xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 dark:text-white",children:"Admin Dashboard"}),(0,a.jsx)("p",{className:"mt-2 text-gray-600 dark:text-gray-300",children:"Manage brands, templates, users, and system settings."})]}),(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("nav",{className:"flex space-x-8",children:[{id:"overview",name:"Overview"},{id:"brands",name:"Brands"},{id:"templates",name:"Templates"},{id:"users",name:"Users"}].map(e=>(0,a.jsx)("button",{onClick:()=>k(e.id),className:`py-2 px-1 border-b-2 font-medium text-sm ${N===e.id?"border-blue-500 text-blue-600 dark:text-blue-400":"border-transparent text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-300 hover:border-gray-300 dark:hover:border-gray-600"}`,children:e.name},e.id))})}),"overview"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6",children:[(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-500 dark:bg-blue-600 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDC65"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Total Users"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:b.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-500 dark:bg-green-600 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83C\uDFE2"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Active Brands"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:i.filter(e=>e.isActive).length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-500 dark:bg-purple-600 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCC4"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"Templates"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:g.length})]})]})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-500 dark:bg-orange-600 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCCA"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500 dark:text-gray-400",children:"BVAs Created"}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:v.length})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-6",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent Users"}),(0,a.jsx)("div",{className:"space-y-3",children:b.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.email}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:new Date(e.createdAt).toLocaleDateString()})]}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"admin"===e.role?"bg-red-100 text-red-800":"bg-blue-100 text-blue-800"}`,children:e.role})]},e.id))})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Recent BVAs"}),(0,a.jsx)("div",{className:"space-y-3",children:v.slice(0,5).map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("p",{className:"text-xs text-gray-500",children:e.clientName})]}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"completed"===e.status?"bg-green-100 text-green-800":"in-progress"===e.status?"bg-yellow-100 text-yellow-800":"bg-gray-100 text-gray-800"}`,children:e.status})]},e.id))})]})})]})]}),"brands"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Brands Management"}),(0,a.jsx)("button",{onClick:()=>C(!0),className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Add New Brand"})]}),A&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Create New Brand"}),(0,a.jsxs)("form",{onSubmit:z,className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Brand Name *"}),(0,a.jsx)("input",{type:"text",value:P.name,onChange:e=>S({...P,name:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Description"}),(0,a.jsx)("textarea",{value:P.description,onChange:e=>S({...P,description:e.target.value}),rows:3,className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Logo URL"}),(0,a.jsx)("input",{type:"url",value:P.logoUrl,onChange:e=>S({...P,logoUrl:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:[(0,a.jsx)("button",{type:"submit",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Create Brand"}),(0,a.jsx)("button",{type:"button",onClick:()=>C(!1),className:"bg-gray-300 text-gray-700 px-4 py-2 rounded-md font-medium hover:bg-gray-400",children:"Cancel"})]})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Templates"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:i.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[e.logoUrl&&(0,a.jsx)("img",{className:"h-8 w-8 rounded mr-3",src:e.logoUrl,alt:e.name}),(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:g.filter(t=>t.brandId===e.id).length}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"}`,children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:[(0,a.jsx)("button",{onClick:()=>T(e.id,e.isActive),className:`mr-3 ${e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"}`,children:e.isActive?"Deactivate":"Activate"}),(0,a.jsx)(n(),{href:`/admin/brands/${e.id}`,className:"text-blue-600 hover:text-blue-900",children:"Edit"})]})]},e.id))})]})})]}),"templates"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900",children:"Templates Management"}),(0,a.jsx)(n(),{href:"/admin/templates/new",className:"bg-blue-600 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700",children:"Create New Template"})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow overflow-hidden",children:(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,a.jsx)("thead",{className:"bg-gray-50",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Template"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Brand"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Category"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Version"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Progress"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:g.map(e=>{let t=i.find(t=>t.id===e.brandId),r=e.step1Completed||!1,s=e.step2Completed||!1,d=r&&s?100:50*!!r;return(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900",children:e.name}),(0,a.jsx)("div",{className:"text-sm text-gray-500",children:e.description})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:t?.name||"Unknown"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.templateVersion||"1.0"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-16 bg-gray-200 rounded-full h-2 mr-2",children:(0,a.jsx)("div",{className:`h-2 rounded-full ${100===d?"bg-green-500":"bg-blue-500"}`,style:{width:`${d}%`}})}),(0,a.jsxs)("span",{className:"text-xs text-gray-500",children:[d,"%"]})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"published"===e.status?"bg-green-100 text-green-800":"bg-yellow-100 text-yellow-800"}`,children:"published"===e.status?"Published":"Draft"}),(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e.isActive?"bg-blue-100 text-blue-800":"bg-gray-100 text-gray-800"}`,children:e.isActive?"Active":"Inactive"})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)(n(),{href:`/admin/templates/${e.id}/edit`,className:"text-blue-600 hover:text-blue-900",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>G(e.id,e.isActive),className:`text-left ${e.isActive?"text-red-600 hover:text-red-900":"text-green-600 hover:text-green-900"}`,children:e.isActive?"Deactivate":"Activate"})]})})]},e.id)})})]})})]}),"users"===N&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsx)("div",{className:"flex justify-between items-center",children:(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 dark:text-white",children:"User Management"})}),(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow p-6",children:(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Search Users"}),(0,a.jsx)("input",{type:"text",placeholder:"Search by name or email...",value:q,onChange:e=>R(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Filter by Role"}),(0,a.jsxs)("select",{value:$,onChange:e=>M(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Roles"}),(0,a.jsx)("option",{value:"admin",children:"Admin"}),(0,a.jsx)("option",{value:"user",children:"User"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2",children:"Filter by Status"}),(0,a.jsxs)("select",{value:I,onChange:e=>_(e.target.value),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"all",children:"All Status"}),(0,a.jsx)("option",{value:"active",children:"Active"}),(0,a.jsx)("option",{value:"inactive",children:"Inactive"})]})]})]})}),(0,a.jsxs)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow overflow-hidden",children:[(0,a.jsxs)("table",{className:"min-w-full divide-y divide-gray-200 dark:divide-gray-600",children:[(0,a.jsx)("thead",{className:"bg-gray-50 dark:bg-gray-700",children:(0,a.jsxs)("tr",{children:[(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"User"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Role"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Status"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Last Login"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Created"}),(0,a.jsx)("th",{className:"px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",children:"Actions"})]})}),(0,a.jsx)("tbody",{className:"bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-600",children:O.map(e=>(0,a.jsxs)("tr",{children:[(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"text-sm font-medium text-gray-900 dark:text-white",children:e.firstName&&e.lastName?`${e.firstName} ${e.lastName}`:e.email}),(0,a.jsx)("div",{className:"text-sm text-gray-500 dark:text-gray-400",children:e.email})]})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${"admin"===e.role?"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200"}`,children:e.role})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,a.jsx)("span",{className:`px-2 py-1 text-xs font-medium rounded-full ${e.isActive?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"}`,children:e.isActive?"Active":"Inactive"})}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:e.lastLoginAt?new Date(e.lastLoginAt).toLocaleDateString():"Never"}),(0,a.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400",children:new Date(e.createdAt).toLocaleDateString()}),(0,a.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium space-x-2",children:[(0,a.jsx)("button",{onClick:()=>V(e),className:"text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300",children:"Edit"}),(0,a.jsx)("button",{onClick:()=>H(e.id,e.isActive),className:`${e.isActive?"text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300":"text-green-600 dark:text-green-400 hover:text-green-900 dark:hover:text-green-300"}`,children:e.isActive?"Disable":"Enable"}),(0,a.jsx)("button",{onClick:()=>J(e.email),className:"text-orange-600 dark:text-orange-400 hover:text-orange-900 dark:hover:text-orange-300",children:"Reset Password"})]})]},e.id))})]}),0===O.length&&(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 dark:text-gray-500 text-4xl mb-4",children:"\uD83D\uDC65"}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white mb-2",children:"No users found"}),(0,a.jsx)("p",{className:"text-gray-600 dark:text-gray-300",children:"Try adjusting your search or filter criteria."})]})]})]})]})}),E&&D&&(0,a.jsx)(h,{user:D,onSave:F,onClose:()=>{U(!1),L(null)}})]}):null}function h({user:e,onSave:t,onClose:r}){let[d,l]=(0,s.useState)({firstName:e.firstName||"",lastName:e.lastName||"",email:e.email,role:e.role,isActive:e.isActive});return(0,a.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,a.jsx)("div",{className:"bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-md w-full",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 dark:text-white",children:"Edit User"}),(0,a.jsxs)("button",{onClick:r,className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300",children:[(0,a.jsx)("span",{className:"sr-only",children:"Close"}),(0,a.jsx)("svg",{className:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,a.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M6 18L18 6M6 6l12 12"})})]})]}),(0,a.jsxs)("form",{onSubmit:e=>{e.preventDefault(),t(d)},className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"First Name"}),(0,a.jsx)("input",{type:"text",value:d.firstName,onChange:e=>l({...d,firstName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter first name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Last Name"}),(0,a.jsx)("input",{type:"text",value:d.lastName,onChange:e=>l({...d,lastName:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",placeholder:"Enter last name"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Email"}),(0,a.jsx)("input",{type:"email",value:d.email,onChange:e=>l({...d,email:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",required:!0})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1",children:"Role"}),(0,a.jsxs)("select",{value:d.role,onChange:e=>l({...d,role:e.target.value}),className:"w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500",children:[(0,a.jsx)("option",{value:"user",children:"User"}),(0,a.jsx)("option",{value:"admin",children:"Admin"})]})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"isActive",checked:d.isActive,onChange:e=>l({...d,isActive:e.target.checked}),className:"h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-gray-600 rounded"}),(0,a.jsx)("label",{htmlFor:"isActive",className:"ml-2 block text-sm text-gray-700 dark:text-gray-300",children:"Active User"})]}),(0,a.jsxs)("div",{className:"flex space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",className:"flex-1 bg-blue-600 dark:bg-blue-700 text-white px-4 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800",children:"Save Changes"}),(0,a.jsx)("button",{type:"button",onClick:r,className:"flex-1 bg-gray-300 dark:bg-gray-600 text-gray-700 dark:text-gray-300 px-4 py-2 rounded-md font-medium hover:bg-gray-400 dark:hover:bg-gray-500",children:"Cancel"})]})]})]})})})}},21820:e=>{"use strict";e.exports=require("os")},27910:e=>{"use strict";e.exports=require("stream")},28354:e=>{"use strict";e.exports=require("util")},29021:e=>{"use strict";e.exports=require("fs")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30474:(e,t,r)=>{"use strict";r.d(t,{default:()=>s.a});var a=r(31261),s=r.n(a)},31261:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return n},getImageProps:function(){return i}});let a=r(37366),s=r(44953),d=r(46533),l=a._(r(1933));function i(e){let{props:t}=(0,s.getImgProps)(e,{defaultLoader:l.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!1}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let n=d.Image},31769:(e,t,r)=>{"use strict";r.d(t,{A:()=>c,I:()=>x});var a=r(60687),s=r(43210),d=r(85814),l=r.n(d),i=r(51108),n=r(53836);function c(){let{user:e}=(0,i.A)(),[t,r]=(0,s.useState)(!1);if(!e||!e.isTrialUser||"admin"===e.role||t)return null;let{message:d,type:c,daysRemaining:x}=(0,n.Mo)(e);if(!d)return null;let o=()=>{switch(c){case"error":return"text-red-400 dark:text-red-300";case"warning":return"text-yellow-400 dark:text-yellow-300";default:return"text-blue-400 dark:text-blue-300"}};return(0,a.jsx)("div",{className:`border-l-4 p-4 ${(()=>{switch(c){case"error":return"bg-red-50 dark:bg-red-900/20 border-red-200 dark:border-red-800 text-red-800 dark:text-red-200";case"warning":return"bg-yellow-50 dark:bg-yellow-900/20 border-yellow-200 dark:border-yellow-800 text-yellow-800 dark:text-yellow-200";default:return"bg-blue-50 dark:bg-blue-900/20 border-blue-200 dark:border-blue-800 text-blue-800 dark:text-blue-200"}})()}`,children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:"error"===c?(0,a.jsx)("svg",{className:`h-5 w-5 ${o()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z",clipRule:"evenodd"})}):"warning"===c?(0,a.jsx)("svg",{className:`h-5 w-5 ${o()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})}):(0,a.jsx)("svg",{className:`h-5 w-5 ${o()}`,viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z",clipRule:"evenodd"})})}),(0,a.jsx)("div",{className:"ml-3",children:(0,a.jsx)("p",{className:"text-sm font-medium",children:d})})]}),(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(l(),{href:"/pricing",className:`px-4 py-2 rounded-md text-sm font-medium transition-colors ${(()=>{switch(c){case"error":return"bg-red-600 dark:bg-red-700 hover:bg-red-700 dark:hover:bg-red-800 text-white";case"warning":return"bg-yellow-600 dark:bg-yellow-700 hover:bg-yellow-700 dark:hover:bg-yellow-800 text-white";default:return"bg-blue-600 dark:bg-blue-700 hover:bg-blue-700 dark:hover:bg-blue-800 text-white"}})()}`,children:"Upgrade Now"}),(0,a.jsx)("button",{onClick:()=>r(!0),className:"text-gray-400 hover:text-gray-600 dark:hover:text-gray-300","aria-label":"Dismiss banner",children:(0,a.jsx)("svg",{className:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z",clipRule:"evenodd"})})})]})]})})}function x(){let{user:e}=(0,i.A)();if(!e||!e.isTrialUser||"admin"===e.role)return null;let{daysRemaining:t}=(0,n.Mo)(e);return t<=0?(0,a.jsx)(l(),{href:"/pricing",className:"px-3 py-1 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200 text-xs font-medium rounded-full hover:bg-red-200 dark:hover:bg-red-800 transition-colors",children:"Trial Expired"}):(0,a.jsxs)(l(),{href:"/pricing",className:`px-3 py-1 text-xs font-medium rounded-full transition-colors ${t<=3?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200 hover:bg-yellow-200 dark:hover:bg-yellow-800":"bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 hover:bg-blue-200 dark:hover:bg-blue-800"}`,children:[t," day",1===t?"":"s"," left"]})}},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},49441:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});let a=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\source\\\\repos\\\\ravihani\\\\valtics\\\\valtics-ai\\\\app\\\\admin\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\page.tsx","default")},55511:e=>{"use strict";e.exports=require("crypto")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},73496:e=>{"use strict";e.exports=require("http2")},74075:e=>{"use strict";e.exports=require("zlib")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},98656:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>o,pages:()=>x,routeModule:()=>m,tree:()=>c});var a=r(65239),s=r(48088),d=r(88170),l=r.n(d),i=r(30893),n={};for(let e in i)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(n[e]=()=>i[e]);r.d(t,n);let c={children:["",{children:["admin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,49441)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,58014)),"C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,57398,23)),"next/dist/client/components/not-found-error"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,46055))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]}.children,x=["C:\\Users\\<USER>\\source\\repos\\ravihani\\valtics\\valtics-ai\\app\\admin\\page.tsx"],o={require:r,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:s.RouteKind.APP_PAGE,page:"/admin/page",pathname:"/admin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[447,823,567,533,77],()=>r(98656));module.exports=a})();