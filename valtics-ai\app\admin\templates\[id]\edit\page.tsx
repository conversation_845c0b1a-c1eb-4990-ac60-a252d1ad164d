'use client';

import { useEffect, useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useRouter, useParams } from 'next/navigation';
import Link from 'next/link';
import { doc, getDoc, updateDoc, Timestamp } from 'firebase/firestore';
import { db } from '@/lib/firebase/config';
import { Template, Brand } from '@/types';
import Navigation from '@/components/Navigation';
import RichTextEditor from '@/components/RichTextEditor';
import SimpleRichTextEditor from '@/components/SimpleRichTextEditor';
import FileUpload from '@/components/FileUpload';
import { StorageService } from '@/lib/firebase/storage';

export default function EditTemplate() {
  const { user, loading, isAdmin } = useAuth();
  const router = useRouter();
  const params = useParams();
  const templateId = params.id as string;

  const [template, setTemplate] = useState<Template | null>(null);
  const [brand, setBrand] = useState<Brand | null>(null);
  const [loadingData, setLoadingData] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [currentStep, setCurrentStep] = useState<'step1' | 'step2'>('step2');
  const [useSimpleEditor, setUseSimpleEditor] = useState(true); // Default to simple editor for React 19 compatibility

  const [formData, setFormData] = useState({
    enterpriseNeed: '',
    solutionDescriptionDocument: '',
    riskOfNoInvestment: '',
    enterpriseNeedFileUrl: '',
    solutionDescriptionFileUrl: '',
    riskOfNoInvestmentFileUrl: ''
  });

  const [uploadingFiles, setUploadingFiles] = useState({
    enterpriseNeed: false,
    solutionDescription: false,
    riskOfNoInvestment: false
  });

  const [step1Data, setStep1Data] = useState({
    solutionProviderName: '',
    solutionName: '',
    solutionDescription: '',
    templateVersion: '',
    templateVersionDate: '',
    category: '',
    price: 0
  });

  useEffect(() => {
    if (!loading && !isAdmin) {
      router.push('/');
      return;
    }
    if (isAdmin && templateId) {
      fetchTemplateData();
    }
  }, [loading, isAdmin, router, templateId]);

  const fetchTemplateData = async () => {
    try {
      setLoadingData(true);

      // Fetch template
      const templateDoc = await getDoc(doc(db, 'templates', templateId));
      if (templateDoc.exists()) {
        const templateData = { id: templateDoc.id, ...templateDoc.data() } as Template;
        setTemplate(templateData);

        // Set form data
        setFormData({
          enterpriseNeed: templateData.enterpriseNeed || '',
          solutionDescriptionDocument: templateData.solutionDescriptionDocument || '',
          riskOfNoInvestment: templateData.riskOfNoInvestment || '',
          enterpriseNeedFileUrl: templateData.enterpriseNeedFileUrl || '',
          solutionDescriptionFileUrl: templateData.solutionDescriptionFileUrl || '',
          riskOfNoInvestmentFileUrl: templateData.riskOfNoInvestmentFileUrl || ''
        });

        setStep1Data({
          solutionProviderName: templateData.solutionProviderName || '',
          solutionName: templateData.solutionName || '',
          solutionDescription: templateData.solutionDescription || '',
          templateVersion: templateData.templateVersion || '',
          templateVersionDate: templateData.templateVersionDate ?
            (templateData.templateVersionDate instanceof Date
              ? templateData.templateVersionDate.toISOString().split('T')[0]
              : new Date((templateData.templateVersionDate as any).seconds * 1000).toISOString().split('T')[0]
            ) : '',
          category: templateData.category || '',
          price: templateData.price || 0
        });

        // Fetch brand
        const brandDoc = await getDoc(doc(db, 'brands', templateData.brandId));
        if (brandDoc.exists()) {
          setBrand({ id: brandDoc.id, ...brandDoc.data() } as Brand);
        }
      } else {
        router.push('/admin');
      }
    } catch (error) {
      console.error('Error fetching template data:', error);
      router.push('/admin');
    } finally {
      setLoadingData(false);
    }
  };

  const handleStep1Update = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        solutionProviderName: step1Data.solutionProviderName,
        solutionName: step1Data.solutionName,
        solutionDescription: step1Data.solutionDescription,
        templateVersion: step1Data.templateVersion,
        templateVersionDate: Timestamp.fromDate(new Date(step1Data.templateVersionDate)),
        category: step1Data.category,
        price: step1Data.price,
        name: step1Data.solutionName, // Update template name
        description: step1Data.solutionDescription, // Update template description
        updatedAt: Timestamp.now()
      });

      alert('Step 1 information updated successfully!');
      await fetchTemplateData(); // Refresh data

    } catch (error) {
      console.error('Error updating template:', error);
      alert('Error updating template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleFileUpload = async (
    file: File,
    documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment'
  ) => {
    if (!template) return;

    try {
      setUploadingFiles(prev => ({ ...prev, [documentType]: true }));

      const fileUrl = await StorageService.uploadTemplateDocument(file, templateId, documentType);

      // Update form data
      const fieldName = `${documentType}FileUrl` as keyof typeof formData;
      setFormData(prev => ({ ...prev, [fieldName]: fileUrl }));

      // Update database immediately
      await updateDoc(doc(db, 'templates', templateId), {
        [`${documentType}FileUrl`]: fileUrl,
        updatedAt: Timestamp.now()
      });

      alert('File uploaded successfully!');

    } catch (error) {
      console.error('Error uploading file:', error);
      alert('Error uploading file. Please try again.');
    } finally {
      setUploadingFiles(prev => ({ ...prev, [documentType]: false }));
    }
  };

  const handleFileRemove = async (documentType: 'enterpriseNeed' | 'solutionDescription' | 'riskOfNoInvestment') => {
    if (!template) return;

    try {
      const fieldName = `${documentType}FileUrl` as keyof typeof formData;
      const currentUrl = formData[fieldName];

      if (currentUrl) {
        // Delete from storage
        await StorageService.deleteFile(currentUrl);
      }

      // Update form data
      setFormData(prev => ({ ...prev, [fieldName]: '' }));

      // Update database
      await updateDoc(doc(db, 'templates', templateId), {
        [`${documentType}FileUrl`]: '',
        updatedAt: Timestamp.now()
      });

      alert('File removed successfully!');

    } catch (error) {
      console.error('Error removing file:', error);
      alert('Error removing file. Please try again.');
    }
  };

  const handleStep2Submit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!template) return;

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        enterpriseNeed: formData.enterpriseNeed,
        solutionDescriptionDocument: formData.solutionDescriptionDocument,
        riskOfNoInvestment: formData.riskOfNoInvestment,
        enterpriseNeedFileUrl: formData.enterpriseNeedFileUrl,
        solutionDescriptionFileUrl: formData.solutionDescriptionFileUrl,
        riskOfNoInvestmentFileUrl: formData.riskOfNoInvestmentFileUrl,
        step2Completed: true,
        updatedAt: Timestamp.now()
      });

      alert('Documents saved successfully! You can now publish the template.');
      await fetchTemplateData(); // Refresh data

    } catch (error) {
      console.error('Error updating template:', error);
      alert('Error updating template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handlePublishTemplate = async () => {
    if (!template) return;

    if (!template.step1Completed || !template.step2Completed) {
      alert('Please complete both steps before publishing.');
      return;
    }

    if (!formData.enterpriseNeed || !formData.solutionDescriptionDocument || !formData.riskOfNoInvestment) {
      alert('Please fill in all three documents before publishing.');
      return;
    }

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        status: 'published',
        isActive: true,
        updatedAt: Timestamp.now()
      });

      alert('Template published successfully!');
      router.push('/admin');

    } catch (error) {
      console.error('Error publishing template:', error);
      alert('Error publishing template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  const handleSaveDraft = async () => {
    if (!template) return;

    try {
      setSubmitting(true);

      await updateDoc(doc(db, 'templates', templateId), {
        enterpriseNeed: formData.enterpriseNeed,
        solutionDescriptionDocument: formData.solutionDescriptionDocument,
        riskOfNoInvestment: formData.riskOfNoInvestment,
        enterpriseNeedFileUrl: formData.enterpriseNeedFileUrl,
        solutionDescriptionFileUrl: formData.solutionDescriptionFileUrl,
        riskOfNoInvestmentFileUrl: formData.riskOfNoInvestmentFileUrl,
        status: 'draft',
        updatedAt: Timestamp.now()
      });

      alert('Template saved as draft successfully!');

    } catch (error) {
      console.error('Error saving template:', error);
      alert('Error saving template. Please try again.');
    } finally {
      setSubmitting(false);
    }
  };

  if (loading || loadingData) {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <div className="text-xl">Loading...</div>
      </div>
    );
  }

  if (!isAdmin || !template) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <Navigation />

      <div className="max-w-6xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          {/* Header */}
          <div className="mb-8">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Edit Template</h1>
                <p className="mt-2 text-gray-600 dark:text-gray-300">
                  {template.solutionName} - {brand?.name}
                </p>
                <div className="mt-2 flex items-center space-x-4">
                  <span className={`px-2 py-1 text-xs font-medium rounded-full ${
                    template.status === 'published'
                      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                      : 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                  }`}>
                    {template.status === 'published' ? 'Published' : 'Draft'}
                  </span>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    Version {template.templateVersion}
                  </span>
                </div>
              </div>
              <Link
                href="/admin"
                className="bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-gray-200 px-4 py-2 rounded-md font-medium hover:bg-gray-300 dark:hover:bg-gray-600"
              >
                Back to Admin
              </Link>
            </div>
          </div>

          {/* Progress Indicator */}
          <div className="mb-8">
            <div className="flex items-center">
              <div className={`flex items-center ${template.step1Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
                <div className={`flex items-center justify-center w-8 h-8 ${template.step1Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>
                  {template.step1Completed ? '✓' : '1'}
                </div>
                <span className="ml-2 text-sm font-medium">Basic Information</span>
              </div>
              <div className="flex-1 mx-4 h-0.5 bg-gray-200 dark:bg-gray-700"></div>
              <div className={`flex items-center ${template.step2Completed ? 'text-green-600 dark:text-green-400' : 'text-blue-600 dark:text-blue-400'}`}>
                <div className={`flex items-center justify-center w-8 h-8 ${template.step2Completed ? 'bg-green-600 dark:bg-green-500' : 'bg-blue-600 dark:bg-blue-500'} text-white rounded-full text-sm font-medium`}>
                  {template.step2Completed ? '✓' : '2'}
                </div>
                <span className="ml-2 text-sm font-medium">Documents</span>
              </div>
            </div>
          </div>

          {/* Step Navigation */}
          <div className="mb-6">
            <div className="flex space-x-1 bg-gray-100 dark:bg-gray-800 p-1 rounded-lg">
              <button
                onClick={() => setCurrentStep('step1')}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentStep === 'step1'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                }`}
              >
                Edit Basic Information
              </button>
              <button
                onClick={() => setCurrentStep('step2')}
                className={`flex-1 py-2 px-4 text-sm font-medium rounded-md transition-colors ${
                  currentStep === 'step2'
                    ? 'bg-white dark:bg-gray-700 text-gray-900 dark:text-white shadow'
                    : 'text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200'
                }`}
              >
                Edit Documents
              </button>
            </div>
          </div>

          {/* Step 1 Content */}
          {currentStep === 'step1' && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <form onSubmit={handleStep1Update} className="p-6 space-y-6">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Basic Information</h3>

                {/* Solution Provider Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Solution Provider Name *
                  </label>
                  <input
                    type="text"
                    value={step1Data.solutionProviderName}
                    onChange={(e) => setStep1Data(prev => ({ ...prev, solutionProviderName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                {/* Solution Name */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Solution Name *
                  </label>
                  <input
                    type="text"
                    value={step1Data.solutionName}
                    onChange={(e) => setStep1Data(prev => ({ ...prev, solutionName: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                </div>

                {/* Solution Description */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Solution Description *
                    </label>
                    <button
                      type="button"
                      onClick={() => setUseSimpleEditor(!useSimpleEditor)}
                      className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}
                    </button>
                  </div>
                  {useSimpleEditor ? (
                    <SimpleRichTextEditor
                      value={step1Data.solutionDescription}
                      onChange={(value) => setStep1Data(prev => ({ ...prev, solutionDescription: value }))}
                      placeholder="Enter detailed solution description..."
                      className="border border-gray-300 dark:border-gray-600 rounded-md"
                    />
                  ) : (
                    <RichTextEditor
                      value={step1Data.solutionDescription}
                      onChange={(value) => setStep1Data(prev => ({ ...prev, solutionDescription: value }))}
                      placeholder="Enter detailed solution description..."
                      className="border border-gray-300 dark:border-gray-600 rounded-md"
                    />
                  )}
                </div>

                {/* Template Version and Date */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Template Version *
                    </label>
                    <input
                      type="text"
                      value={step1Data.templateVersion}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, templateVersion: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Template Version Date *
                    </label>
                    <input
                      type="date"
                      value={step1Data.templateVersionDate}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, templateVersionDate: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                      required
                    />
                  </div>
                </div>

                {/* Category and Price */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Category
                    </label>
                    <select
                      value={step1Data.category}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, category: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="Business Analysis">Business Analysis</option>
                      <option value="Financial Planning">Financial Planning</option>
                      <option value="Technology Assessment">Technology Assessment</option>
                      <option value="Risk Management">Risk Management</option>
                      <option value="Strategic Planning">Strategic Planning</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Price ($)
                    </label>
                    <input
                      type="number"
                      min="0"
                      step="0.01"
                      value={step1Data.price}
                      onChange={(e) => setStep1Data(prev => ({ ...prev, price: parseFloat(e.target.value) || 0 }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                </div>

                {/* Submit Button */}
                <div className="flex justify-end pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="submit"
                    disabled={submitting}
                    className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed"
                  >
                    {submitting ? 'Updating...' : 'Update Basic Information'}
                  </button>
                </div>
              </form>
            </div>
          )}

          {/* Step 2 Content */}
          {currentStep === 'step2' && (
            <div className="bg-white dark:bg-gray-800 shadow rounded-lg">
              <form onSubmit={handleStep2Submit} className="p-6 space-y-8">
                <div>
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">Generate Documents</h3>
                  <p className="text-sm text-gray-600 dark:text-gray-400 mb-6">
                    Create the three essential documents for your Business Value Analysis template.
                  </p>
                </div>

                {/* Enterprise Need */}
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300">
                      Enterprise Need *
                    </label>
                    <button
                      type="button"
                      onClick={() => setUseSimpleEditor(!useSimpleEditor)}
                      className="text-xs text-blue-600 dark:text-blue-400 hover:underline"
                    >
                      {useSimpleEditor ? 'Try Advanced Editor' : 'Use Simple Editor'}
                    </button>
                  </div>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    Describe the business need or challenge that this solution addresses.
                  </p>

                  {/* Text Editor */}
                  <div className="mb-4">
                    {useSimpleEditor ? (
                      <SimpleRichTextEditor
                        value={formData.enterpriseNeed}
                        onChange={(value) => setFormData(prev => ({ ...prev, enterpriseNeed: value }))}
                        placeholder="Describe the enterprise need that this solution addresses..."
                        className="border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    ) : (
                      <RichTextEditor
                        value={formData.enterpriseNeed}
                        onChange={(value) => setFormData(prev => ({ ...prev, enterpriseNeed: value }))}
                        placeholder="Describe the enterprise need that this solution addresses..."
                        className="border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    )}
                  </div>

                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Upload Supporting Document or Image (Optional)
                    </label>
                    <FileUpload
                      onFileSelect={(file) => handleFileUpload(file, 'enterpriseNeed')}
                      onFileRemove={() => handleFileRemove('enterpriseNeed')}
                      currentFileUrl={formData.enterpriseNeedFileUrl}
                      currentFileName={formData.enterpriseNeedFileUrl ? StorageService.getFileNameFromUrl(formData.enterpriseNeedFileUrl) : undefined}
                      disabled={uploadingFiles.enterpriseNeed}
                      className="mt-2"
                    />
                  </div>
                </div>

                {/* Solution Description Document */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Solution Description *
                  </label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    Provide a comprehensive description of how your solution works and its key features.
                  </p>

                  {/* Text Editor */}
                  <div className="mb-4">
                    {useSimpleEditor ? (
                      <SimpleRichTextEditor
                        value={formData.solutionDescriptionDocument}
                        onChange={(value) => setFormData(prev => ({ ...prev, solutionDescriptionDocument: value }))}
                        placeholder="Provide a detailed description of your solution..."
                        className="border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    ) : (
                      <RichTextEditor
                        value={formData.solutionDescriptionDocument}
                        onChange={(value) => setFormData(prev => ({ ...prev, solutionDescriptionDocument: value }))}
                        placeholder="Provide a detailed description of your solution..."
                        className="border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    )}
                  </div>

                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Upload Supporting Document or Image (Optional)
                    </label>
                    <FileUpload
                      onFileSelect={(file) => handleFileUpload(file, 'solutionDescription')}
                      onFileRemove={() => handleFileRemove('solutionDescription')}
                      currentFileUrl={formData.solutionDescriptionFileUrl}
                      currentFileName={formData.solutionDescriptionFileUrl ? StorageService.getFileNameFromUrl(formData.solutionDescriptionFileUrl) : undefined}
                      disabled={uploadingFiles.solutionDescription}
                      className="mt-2"
                    />
                  </div>
                </div>

                {/* Risk of No Investment */}
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Risk of No Investment *
                  </label>
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
                    Explain the potential risks and consequences of not implementing this solution.
                  </p>

                  {/* Text Editor */}
                  <div className="mb-4">
                    {useSimpleEditor ? (
                      <SimpleRichTextEditor
                        value={formData.riskOfNoInvestment}
                        onChange={(value) => setFormData(prev => ({ ...prev, riskOfNoInvestment: value }))}
                        placeholder="Describe the risks of not investing in this solution..."
                        className="border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    ) : (
                      <RichTextEditor
                        value={formData.riskOfNoInvestment}
                        onChange={(value) => setFormData(prev => ({ ...prev, riskOfNoInvestment: value }))}
                        placeholder="Describe the risks of not investing in this solution..."
                        className="border border-gray-300 dark:border-gray-600 rounded-md"
                      />
                    )}
                  </div>

                  {/* File Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                      Upload Supporting Document or Image (Optional)
                    </label>
                    <FileUpload
                      onFileSelect={(file) => handleFileUpload(file, 'riskOfNoInvestment')}
                      onFileRemove={() => handleFileRemove('riskOfNoInvestment')}
                      currentFileUrl={formData.riskOfNoInvestmentFileUrl}
                      currentFileName={formData.riskOfNoInvestmentFileUrl ? StorageService.getFileNameFromUrl(formData.riskOfNoInvestmentFileUrl) : undefined}
                      disabled={uploadingFiles.riskOfNoInvestment}
                      className="mt-2"
                    />
                  </div>
                </div>

                {/* Action Buttons */}
                <div className="flex justify-between pt-6 border-t border-gray-200 dark:border-gray-700">
                  <button
                    type="button"
                    onClick={handleSaveDraft}
                    disabled={submitting}
                    className="bg-gray-600 dark:bg-gray-700 text-white px-6 py-2 rounded-md font-medium hover:bg-gray-700 dark:hover:bg-gray-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                  >
                    {submitting && (
                      <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                      </svg>
                    )}
                    <span>{submitting ? 'Saving...' : 'Save as Draft'}</span>
                  </button>

                  <div className="flex space-x-3">
                    <button
                      type="submit"
                      disabled={submitting}
                      className="bg-blue-600 dark:bg-blue-700 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 dark:hover:bg-blue-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                    >
                      {submitting && (
                        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                        </svg>
                      )}
                      <span>{submitting ? 'Saving...' : 'Save Documents'}</span>
                    </button>

                    {template.step2Completed && (
                      <button
                        type="button"
                        onClick={handlePublishTemplate}
                        disabled={submitting || template.status === 'published'}
                        className="bg-green-600 dark:bg-green-700 text-white px-6 py-2 rounded-md font-medium hover:bg-green-700 dark:hover:bg-green-800 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                      >
                        {submitting && template.status !== 'published' && (
                          <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                          </svg>
                        )}
                        <span>{template.status === 'published' ? 'Published' : submitting ? 'Publishing...' : 'Publish Template'}</span>
                      </button>
                    )}
                  </div>
                </div>
              </form>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
