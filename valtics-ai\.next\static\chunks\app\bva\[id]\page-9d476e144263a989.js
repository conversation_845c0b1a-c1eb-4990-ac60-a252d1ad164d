(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[132],{1138:(e,s,t)=>{"use strict";t.d(s,{db:()=>c,j2:()=>n});var a=t(3915),i=t(6203),r=t(5317),d=t(858);let l=(0,a.Dk)().length?(0,a.Sx)():(0,a.Wp)({apiKey:"AIzaSyDk_NWWIicxaEXYSWIoM-24-d9QrnrMhrg",authDomain:"ai-app-d5ee6.firebaseapp.com",projectId:"ai-app-d5ee6",storageBucket:"ai-app-d5ee6.firebasestorage.app",messagingSenderId:"81717928007",appId:"1:81717928007:web:c21e9ccb3abae564f29162"}),n=(0,i.xI)(l),c=(0,r.aU)(l);(0,d.c7)(l)},3274:(e,s,t)=>{"use strict";t.d(s,{A:()=>x,AuthProvider:()=>m});var a=t(5155),i=t(2115),r=t(6203),d=t(1138),l=t(5317),n=t(9886);let c=(0,i.createContext)(null),m=e=>{let{children:s}=e,[t,m]=(0,i.useState)(null),[x,o]=(0,i.useState)(null),[u,h]=(0,i.useState)(!0),[g,p]=(0,i.useState)(!1),[f,j]=(0,i.useState)(!1),[N,b]=(0,i.useState)(!1),y=async()=>{if(x)try{let e=await (0,l.x7)((0,l.H9)(d.db,"users",x.uid));if(e.exists()){let s={id:e.id,...e.data()},t=(0,n.gZ)(s);t&&!s.trialExpired&&(await (0,l.mZ)((0,l.H9)(d.db,"users",x.uid),{trialExpired:!0,updatedAt:new Date}),s.trialExpired=!0),m(s),p("admin"===s.role),j((0,n.nE)(s)),b(t)}}catch(e){console.error("Error refreshing user data:",e)}};(0,i.useEffect)(()=>{let e=(0,r.hg)(d.j2,async e=>{o(e),e?await y():(m(null),p(!1),j(!1),b(!1)),h(!1)});return()=>e()},[]),(0,i.useEffect)(()=>{x&&y()},[x]);let v=async(e,s)=>{await (0,r.x9)(d.j2,e,s)},w=async(e,s)=>{let t=await (0,r.eJ)(d.j2,e,s),a=(0,n.ow)(t.user.email||e);await (0,l.BN)((0,l.H9)(d.db,"users",t.user.uid),a)},D=async()=>{await (0,r.CI)(d.j2)},E=async()=>{let e=new r.HF,s=await (0,r.df)(d.j2,e);if(!(await (0,l.x7)((0,l.H9)(d.db,"users",s.user.uid))).exists()){var t,a;let e=(0,n.ow)(s.user.email||"",{firstName:null==(t=s.user.displayName)?void 0:t.split(" ")[0],lastName:null==(a=s.user.displayName)?void 0:a.split(" ").slice(1).join(" ")});await (0,l.BN)((0,l.H9)(d.db,"users",s.user.uid),e)}},S=async()=>{let e=new r.sk,s=await (0,r.df)(d.j2,e);if(!(await (0,l.x7)((0,l.H9)(d.db,"users",s.user.uid))).exists()){var t,a;let e=(0,n.ow)(s.user.email||"",{firstName:null==(t=s.user.displayName)?void 0:t.split(" ")[0],lastName:null==(a=s.user.displayName)?void 0:a.split(" ").slice(1).join(" ")});await (0,l.BN)((0,l.H9)(d.db,"users",s.user.uid),e)}};return(0,a.jsx)(c.Provider,{value:{user:t,firebaseUser:x,loading:u,signIn:v,signUp:w,logOut:D,signInWithGoogle:E,signInWithFacebook:S,isAdmin:g,canAccessPremiumFeatures:f,isTrialExpired:N,refreshUserData:y},children:s})},x=()=>{let e=(0,i.useContext)(c);if(!e)throw Error("useAuth must be used within an AuthProvider");return e}},4872:(e,s,t)=>{Promise.resolve().then(t.bind(t,9209))},5695:(e,s,t)=>{"use strict";var a=t(8999);t.o(a,"useParams")&&t.d(s,{useParams:function(){return a.useParams}}),t.o(a,"useRouter")&&t.d(s,{useRouter:function(){return a.useRouter}}),t.o(a,"useSearchParams")&&t.d(s,{useSearchParams:function(){return a.useSearchParams}})},9209:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>x});var a=t(5155),i=t(2115),r=t(3274),d=t(5695),l=t(6874),n=t.n(l),c=t(5317),m=t(1138);function x(){var e,s,t;let{user:l,firebaseUser:x,loading:o}=(0,r.A)(),u=(0,d.useRouter)(),h=(0,d.useParams)().id,[g,p]=(0,i.useState)(null),[f,j]=(0,i.useState)(null),[N,b]=(0,i.useState)(null),[y,v]=(0,i.useState)(!0),[w,D]=(0,i.useState)(!1);(0,i.useEffect)(()=>{o||l||u.push("/login")},[l,o,u]),(0,i.useEffect)(()=>{l&&x&&h&&E()},[l,x,h]);let E=async()=>{try{v(!0);let e=await (0,c.x7)((0,c.H9)(m.db,"bvaInstances",h));if(e.exists()){let s={id:e.id,...e.data()};if(s.userId!==(null==x?void 0:x.uid))return void u.push("/dashboard");if(p(s),s.templateId){let e=await (0,c.x7)((0,c.H9)(m.db,"templates",s.templateId));if(e.exists()){let s={id:e.id,...e.data()};j(s);let t=await (0,c.x7)((0,c.H9)(m.db,"brands",s.brandId));t.exists()&&b({id:t.id,...t.data()})}}}else u.push("/dashboard")}catch(e){console.error("Error fetching BVA data:",e),u.push("/dashboard")}finally{v(!1)}},S=async()=>{if(g)try{D(!0),await new Promise(e=>setTimeout(e,3e3)),await (0,c.mZ)((0,c.H9)(m.db,"bvaInstances",g.id),{status:"completed",completedAt:new Date,updatedAt:new Date}),await E()}catch(e){console.error("Error generating report:",e),alert("Error generating report. Please try again.")}finally{D(!1)}};return o||y?(0,a.jsx)("div",{className:"flex min-h-screen items-center justify-center",children:(0,a.jsx)("div",{className:"text-xl",children:"Loading..."})}):l&&g?(0,a.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,a.jsx)("nav",{className:"bg-white shadow-sm border-b",children:(0,a.jsx)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"flex justify-between h-16",children:[(0,a.jsx)("div",{className:"flex items-center",children:(0,a.jsx)(n(),{href:"/dashboard",className:"text-xl font-semibold text-gray-900",children:"VALTICS AI"})}),(0,a.jsx)("div",{className:"flex items-center space-x-4",children:(0,a.jsx)(n(),{href:"/dashboard",className:"text-gray-700 hover:text-gray-900 px-3 py-2 rounded-md text-sm font-medium",children:"← Back to Dashboard"})})]})})}),(0,a.jsx)("div",{className:"max-w-6xl mx-auto py-6 sm:px-6 lg:px-8",children:(0,a.jsxs)("div",{className:"px-4 py-6 sm:px-0",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md overflow-hidden mb-8",children:(0,a.jsx)("div",{className:"p-6",children:(0,a.jsxs)("div",{className:"flex items-start justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:g.name}),(0,a.jsxs)("p",{className:"text-lg text-gray-600 mb-4",children:["Client: ",g.clientName]}),(0,a.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-sm font-medium ".concat((e=>{switch(e){case"completed":return"bg-green-100 text-green-800";case"in-progress":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}})(g.status)),children:g.status.charAt(0).toUpperCase()+g.status.slice(1)}),f&&N&&(0,a.jsxs)("span",{className:"text-sm text-gray-500",children:["Template: ",f.name," by ",N.name]})]})]}),(0,a.jsxs)("div",{className:"flex space-x-3",children:["completed"!==g.status&&(0,a.jsx)("button",{onClick:S,disabled:w,className:"bg-blue-600 text-white px-6 py-2 rounded-md font-medium hover:bg-blue-700 disabled:opacity-50",children:w?"Generating...":"Generate Report"}),(0,a.jsx)(n(),{href:"/bva/".concat(g.id,"/edit"),className:"bg-gray-600 text-white px-6 py-2 rounded-md font-medium hover:bg-gray-700",children:"Edit"})]})]})})}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-green-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"ROI"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Return on Investment"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(()=>{if(!(null==g?void 0:g.data))return 0;let e=g.data.expectedBenefits||0,s=g.data.currentCosts||0;return 0===s?0:(e-s)/s*100})().toFixed(1),"%"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"$"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Annual Benefits"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:["$",(g.data.expectedBenefits||0).toLocaleString()]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"⏱"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Payback Period"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[(()=>{if(!(null==g?void 0:g.data))return 0;let e=g.data.expectedBenefits||0,s=g.data.currentCosts||0;return 0===e?0:s/(e/12)})().toFixed(1)," mo"]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md p-6",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"flex-shrink-0 w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center",children:(0,a.jsx)("span",{className:"text-white text-sm font-bold",children:"\uD83D\uDCCA"})}),(0,a.jsxs)("div",{className:"ml-4",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-500",children:"Analysis Period"}),(0,a.jsxs)("p",{className:"text-2xl font-bold text-gray-900",children:[g.data.timeframe||12," mo"]})]})]})})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8",children:[(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Project Details"}),(0,a.jsxs)("dl",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Industry"}),(0,a.jsx)("dd",{className:"text-sm text-gray-900",children:g.data.industry||"Not specified"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Current Annual Costs"}),(0,a.jsxs)("dd",{className:"text-sm text-gray-900",children:["$",(g.data.currentCosts||0).toLocaleString()]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Expected Annual Benefits"}),(0,a.jsxs)("dd",{className:"text-sm text-gray-900",children:["$",(g.data.expectedBenefits||0).toLocaleString()]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Created"}),(0,a.jsx)("dd",{className:"text-sm text-gray-900",children:new Date(g.createdAt).toLocaleDateString()})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("dt",{className:"text-sm font-medium text-gray-500",children:"Last Updated"}),(0,a.jsx)("dd",{className:"text-sm text-gray-900",children:new Date(g.updatedAt).toLocaleDateString()})]})]})]})}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-md",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Additional Information"}),(null==(e=g.data.additionalData)?void 0:e.businessDrivers)&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Business Drivers"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:g.data.additionalData.businessDrivers})]}),(null==(s=g.data.additionalData)?void 0:s.successMetrics)&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Success Metrics"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:g.data.additionalData.successMetrics})]}),(null==(t=g.data.additionalData)?void 0:t.timeline)&&(0,a.jsxs)("div",{className:"mb-4",children:[(0,a.jsx)("h4",{className:"text-sm font-medium text-gray-500 mb-2",children:"Implementation Timeline"}),(0,a.jsx)("p",{className:"text-sm text-gray-900",children:g.data.additionalData.timeline})]})]})})]}),"completed"===g.status&&(0,a.jsx)("div",{className:"mt-8 bg-white rounded-lg shadow-md",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Generated Reports"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Executive Summary"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Concise overview perfect for stakeholder presentations"}),(0,a.jsx)("button",{className:"bg-blue-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-blue-700",children:"Download PDF"})]}),(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-4",children:[(0,a.jsx)("h4",{className:"font-medium text-gray-900 mb-2",children:"Full Report"}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Detailed analysis with charts, tables, and calculations"}),(0,a.jsx)("button",{className:"bg-green-600 text-white px-4 py-2 rounded text-sm font-medium hover:bg-green-700",children:"Download PDF"})]})]})]})}),"completed"===g.status&&(0,a.jsx)("div",{className:"mt-8 bg-white rounded-lg shadow-md",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-4",children:"Financial Analysis"}),(0,a.jsx)("div",{className:"h-64 bg-gray-100 rounded-lg flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"text-gray-400 text-4xl mb-2",children:"\uD83D\uDCCA"}),(0,a.jsx)("p",{className:"text-gray-500",children:"Interactive charts and graphs would appear here"}),(0,a.jsx)("p",{className:"text-sm text-gray-400 mt-1",children:"ROI trends, cost-benefit analysis, payback timeline"})]})})]})})]})})]}):null}},9886:(e,s,t)=>{"use strict";function a(e){return!!e.isTrialUser&&!!e.trialEndDate&&new Date>new Date(e.trialEndDate)}function i(e){return!("admin"!==e.role&&(!e.subscription||"active"!==e.subscription.status)&&(!e.isTrialUser||a(e)))}function r(e){if(!e.isTrialUser)return{message:"",type:"info",daysRemaining:0};let s=function(e){if(!e.isTrialUser||!e.trialEndDate)return 0;let s=new Date;return Math.max(0,Math.ceil((new Date(e.trialEndDate).getTime()-s.getTime())/864e5))}(e);return s<=0?{message:"Your trial has expired. Upgrade to continue using premium features.",type:"error",daysRemaining:0}:s<=3?{message:"Your trial expires in ".concat(s," day").concat(1===s?"":"s",". Upgrade now to continue."),type:"warning",daysRemaining:s}:{message:"Your trial expires in ".concat(s," days on ").concat(e.trialEndDate?new Date(e.trialEndDate).toLocaleDateString("en-US",{weekday:"long",year:"numeric",month:"long",day:"numeric"}):"","."),type:"info",daysRemaining:s}}function d(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};return{email:e,role:"user",isActive:!0,createdAt:new Date,...function(){let e=new Date,s=new Date(e);return s.setDate(e.getDate()+10),{isTrialUser:!0,trialStartDate:e,trialEndDate:s,trialExpired:!1}}(),...s}}t.d(s,{Mo:()=>r,gZ:()=>a,nE:()=>i,ow:()=>d})}},e=>{var s=s=>e(e.s=s);e.O(0,[992,965,288,874,441,684,358],()=>s(4872)),_N_E=e.O()}]);